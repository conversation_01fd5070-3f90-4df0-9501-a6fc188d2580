import{g as t,c as e,a as r}from"./react-vendor-1382c291.js";var n={exports:{}},o={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(t){function e(t,e){var r=t.length;t.push(e);t:for(;0<r;){var n=r-1>>>1,i=t[n];if(!(0<o(i,e)))break t;t[n]=e,t[r]=i,r=n}}function r(t){return 0===t.length?null:t[0]}function n(t){if(0===t.length)return null;var e=t[0],r=t.pop();if(r!==e){t[0]=r;t:for(var n=0,i=t.length,a=i>>>1;n<a;){var u=2*(n+1)-1,s=t[u],c=u+1,f=t[c];if(0>o(s,r))c<i&&0>o(f,s)?(t[n]=f,t[c]=r,n=c):(t[n]=s,t[u]=r,n=u);else{if(!(c<i&&0>o(f,r)))break t;t[n]=f,t[c]=r,n=c}}}return e}function o(t,e){var r=t.sortIndex-e.sortIndex;return 0!==r?r:t.id-e.id}if("object"==typeof performance&&"function"==typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var a=Date,u=a.now();t.unstable_now=function(){return a.now()-u}}var s=[],c=[],f=1,l=null,h=3,d=!1,p=!1,v=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,_="undefined"!=typeof setImmediate?setImmediate:null;function b(t){for(var o=r(c);null!==o;){if(null===o.callback)n(c);else{if(!(o.startTime<=t))break;n(c),o.sortIndex=o.expirationTime,e(s,o)}o=r(c)}}function w(t){if(v=!1,b(t),!p)if(null!==r(s))p=!0,j(m);else{var e=r(c);null!==e&&R(w,e.startTime-t)}}function m(e,o){p=!1,v&&(v=!1,y(C),C=-1),d=!0;var i=h;try{for(b(o),l=r(s);null!==l&&(!(l.expirationTime>o)||e&&!B());){var a=l.callback;if("function"==typeof a){l.callback=null,h=l.priorityLevel;var u=a(l.expirationTime<=o);o=t.unstable_now(),"function"==typeof u?l.callback=u:l===r(s)&&n(s),b(o)}else n(s);l=r(s)}if(null!==l)var f=!0;else{var g=r(c);null!==g&&R(w,g.startTime-o),f=!1}return f}finally{l=null,h=i,d=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var E,x=!1,O=null,C=-1,S=5,A=-1;function B(){return!(t.unstable_now()-A<S)}function P(){if(null!==O){var e=t.unstable_now();A=e;var r=!0;try{r=O(!0,e)}finally{r?E():(x=!1,O=null)}}else x=!1}if("function"==typeof _)E=function(){_(P)};else if("undefined"!=typeof MessageChannel){var k=new MessageChannel,M=k.port2;k.port1.onmessage=P,E=function(){M.postMessage(null)}}else E=function(){g(P,0)};function j(t){O=t,x||(x=!0,E())}function R(e,r){C=g((function(){e(t.unstable_now())}),r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(t){t.callback=null},t.unstable_continueExecution=function(){p||d||(p=!0,j(m))},t.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):S=0<t?Math.floor(1e3/t):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(s)},t.unstable_next=function(t){switch(h){case 1:case 2:case 3:var e=3;break;default:e=h}var r=h;h=e;try{return t()}finally{h=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var r=h;h=t;try{return e()}finally{h=r}},t.unstable_scheduleCallback=function(n,o,i){var a=t.unstable_now();switch("object"==typeof i&&null!==i?i="number"==typeof(i=i.delay)&&0<i?a+i:a:i=a,n){case 1:var u=-1;break;case 2:u=250;break;case 5:u=1073741823;break;case 4:u=1e4;break;default:u=5e3}return n={id:f++,callback:o,priorityLevel:n,startTime:i,expirationTime:u=i+u,sortIndex:-1},i>a?(n.sortIndex=i,e(c,n),null===r(s)&&n===r(c)&&(v?(y(C),C=-1):v=!0,R(w,i-a))):(n.sortIndex=u,e(s,n),p||d||(p=!0,j(m))),n},t.unstable_shouldYield=B,t.unstable_wrapCallback=function(t){var e=h;return function(){var r=h;h=e;try{return t.apply(this,arguments)}finally{h=r}}}}(o),n.exports=o;var i=n.exports,a={exports:{}};var u={exports:{}};const s=t(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var c;function f(){return c||(c=1,u.exports=function(){var t=t||function(t,r){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==e&&e.crypto&&(n=e.crypto),!n)try{n=s}catch(y){}var o=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(y){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(y){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function t(){}return function(e){var r;return t.prototype=e,r=new t,t.prototype=null,r}}(),a={},u=a.lib={},c=u.Base={extend:function(t){var e=i(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},f=u.WordArray=c.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=e!=r?e:4*t.length},toString:function(t){return(t||h).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes,o=t.sigBytes;if(this.clamp(),n%4)for(var i=0;i<o;i++){var a=r[i>>>2]>>>24-i%4*8&255;e[n+i>>>2]|=a<<24-(n+i)%4*8}else for(var u=0;u<o;u+=4)e[n+u>>>2]=r[u>>>2];return this.sigBytes+=o,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=c.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(o());return new f.init(e,t)}}),l=a.enc={},h=l.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o++){var i=e[o>>>2]>>>24-o%4*8&255;n.push((i>>>4).toString(16)),n.push((15&i).toString(16))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new f.init(r,e/2)}},d=l.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o++){var i=e[o>>>2]>>>24-o%4*8&255;n.push(String.fromCharCode(i))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new f.init(r,e)}},p=l.Utf8={stringify:function(t){try{return decodeURIComponent(escape(d.stringify(t)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(t){return d.parse(unescape(encodeURIComponent(t)))}},v=u.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new f.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=p.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r,n=this._data,o=n.words,i=n.sigBytes,a=this.blockSize,u=i/(4*a),s=(u=e?t.ceil(u):t.max((0|u)-this._minBufferSize,0))*a,c=t.min(4*s,i);if(s){for(var l=0;l<s;l+=a)this._doProcessBlock(o,l);r=o.splice(0,s),n.sigBytes-=c}return new f.init(r,c)},clone:function(){var t=c.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});u.Hasher=v.extend({cfg:c.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){v.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new g.HMAC.init(t,r).finalize(e)}}});var g=a.algo={};return a}(Math);return t}()),u.exports}var l,h={exports:{}};function d(){return l||(l=1,h.exports=function(t){return n=(r=t).lib,o=n.Base,i=n.WordArray,(a=r.x64={}).Word=o.extend({init:function(t,e){this.high=t,this.low=e}}),a.WordArray=o.extend({init:function(t,r){t=this.words=t||[],this.sigBytes=r!=e?r:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],n=0;n<e;n++){var o=t[n];r.push(o.high),r.push(o.low)}return i.create(r,this.sigBytes)},clone:function(){for(var t=o.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}}),t;var e,r,n,o,i,a}(f())),h.exports}var p,v={exports:{}};function g(){return p||(p=1,v.exports=function(t){return function(){if("function"==typeof ArrayBuffer){var e=t.lib.WordArray,r=e.init,n=e.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var e=t.byteLength,n=[],o=0;o<e;o++)n[o>>>2]|=t[o]<<24-o%4*8;r.call(this,n,e)}else r.apply(this,arguments)};n.prototype=e}}(),t.lib.WordArray}(f())),v.exports}var y,_={exports:{}};function b(){return y||(y=1,_.exports=function(t){return function(){var e=t,r=e.lib.WordArray,n=e.enc;function o(t){return t<<8&4278255360|t>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o+=2){var i=e[o>>>2]>>>16-o%4*8&65535;n.push(String.fromCharCode(i))}return n.join("")},parse:function(t){for(var e=t.length,n=[],o=0;o<e;o++)n[o>>>1]|=t.charCodeAt(o)<<16-o%2*16;return r.create(n,2*e)}},n.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i+=2){var a=o(e[i>>>2]>>>16-i%4*8&65535);n.push(String.fromCharCode(a))}return n.join("")},parse:function(t){for(var e=t.length,n=[],i=0;i<e;i++)n[i>>>1]|=o(t.charCodeAt(i)<<16-i%2*16);return r.create(n,2*e)}}}(),t.enc.Utf16}(f())),_.exports}var w,m={exports:{}};function E(){return w||(w=1,m.exports=function(t){return function(){var e=t,r=e.lib.WordArray;function n(t,e,n){for(var o=[],i=0,a=0;a<e;a++)if(a%4){var u=n[t.charCodeAt(a-1)]<<a%4*2|n[t.charCodeAt(a)]>>>6-a%4*2;o[i>>>2]|=u<<24-i%4*8,i++}return r.create(o,i)}e.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp();for(var o=[],i=0;i<r;i+=3)for(var a=(e[i>>>2]>>>24-i%4*8&255)<<16|(e[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|e[i+2>>>2]>>>24-(i+2)%4*8&255,u=0;u<4&&i+.75*u<r;u++)o.push(n.charAt(a>>>6*(3-u)&63));var s=n.charAt(64);if(s)for(;o.length%4;)o.push(s);return o.join("")},parse:function(t){var e=t.length,r=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<r.length;i++)o[r.charCodeAt(i)]=i}var a=r.charAt(64);if(a){var u=t.indexOf(a);-1!==u&&(e=u)}return n(t,e,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),t.enc.Base64}(f())),m.exports}var x,O={exports:{}};function C(){return x||(x=1,O.exports=function(t){return function(){var e=t,r=e.lib.WordArray;function n(t,e,n){for(var o=[],i=0,a=0;a<e;a++)if(a%4){var u=n[t.charCodeAt(a-1)]<<a%4*2|n[t.charCodeAt(a)]>>>6-a%4*2;o[i>>>2]|=u<<24-i%4*8,i++}return r.create(o,i)}e.enc.Base64url={stringify:function(t,e){void 0===e&&(e=!0);var r=t.words,n=t.sigBytes,o=e?this._safe_map:this._map;t.clamp();for(var i=[],a=0;a<n;a+=3)for(var u=(r[a>>>2]>>>24-a%4*8&255)<<16|(r[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|r[a+2>>>2]>>>24-(a+2)%4*8&255,s=0;s<4&&a+.75*s<n;s++)i.push(o.charAt(u>>>6*(3-s)&63));var c=o.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(t,e){void 0===e&&(e=!0);var r=t.length,o=e?this._safe_map:this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var a=0;a<o.length;a++)i[o.charCodeAt(a)]=a}var u=o.charAt(64);if(u){var s=t.indexOf(u);-1!==s&&(r=s)}return n(t,r,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),t.enc.Base64url}(f())),O.exports}var S,A={exports:{}};function B(){return S||(S=1,A.exports=function(t){return function(e){var r=t,n=r.lib,o=n.WordArray,i=n.Hasher,a=r.algo,u=[];!function(){for(var t=0;t<64;t++)u[t]=4294967296*e.abs(e.sin(t+1))|0}();var s=a.MD5=i.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,o=t[n];t[n]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i=this._hash.words,a=t[e+0],s=t[e+1],d=t[e+2],p=t[e+3],v=t[e+4],g=t[e+5],y=t[e+6],_=t[e+7],b=t[e+8],w=t[e+9],m=t[e+10],E=t[e+11],x=t[e+12],O=t[e+13],C=t[e+14],S=t[e+15],A=i[0],B=i[1],P=i[2],k=i[3];A=c(A,B,P,k,a,7,u[0]),k=c(k,A,B,P,s,12,u[1]),P=c(P,k,A,B,d,17,u[2]),B=c(B,P,k,A,p,22,u[3]),A=c(A,B,P,k,v,7,u[4]),k=c(k,A,B,P,g,12,u[5]),P=c(P,k,A,B,y,17,u[6]),B=c(B,P,k,A,_,22,u[7]),A=c(A,B,P,k,b,7,u[8]),k=c(k,A,B,P,w,12,u[9]),P=c(P,k,A,B,m,17,u[10]),B=c(B,P,k,A,E,22,u[11]),A=c(A,B,P,k,x,7,u[12]),k=c(k,A,B,P,O,12,u[13]),P=c(P,k,A,B,C,17,u[14]),A=f(A,B=c(B,P,k,A,S,22,u[15]),P,k,s,5,u[16]),k=f(k,A,B,P,y,9,u[17]),P=f(P,k,A,B,E,14,u[18]),B=f(B,P,k,A,a,20,u[19]),A=f(A,B,P,k,g,5,u[20]),k=f(k,A,B,P,m,9,u[21]),P=f(P,k,A,B,S,14,u[22]),B=f(B,P,k,A,v,20,u[23]),A=f(A,B,P,k,w,5,u[24]),k=f(k,A,B,P,C,9,u[25]),P=f(P,k,A,B,p,14,u[26]),B=f(B,P,k,A,b,20,u[27]),A=f(A,B,P,k,O,5,u[28]),k=f(k,A,B,P,d,9,u[29]),P=f(P,k,A,B,_,14,u[30]),A=l(A,B=f(B,P,k,A,x,20,u[31]),P,k,g,4,u[32]),k=l(k,A,B,P,b,11,u[33]),P=l(P,k,A,B,E,16,u[34]),B=l(B,P,k,A,C,23,u[35]),A=l(A,B,P,k,s,4,u[36]),k=l(k,A,B,P,v,11,u[37]),P=l(P,k,A,B,_,16,u[38]),B=l(B,P,k,A,m,23,u[39]),A=l(A,B,P,k,O,4,u[40]),k=l(k,A,B,P,a,11,u[41]),P=l(P,k,A,B,p,16,u[42]),B=l(B,P,k,A,y,23,u[43]),A=l(A,B,P,k,w,4,u[44]),k=l(k,A,B,P,x,11,u[45]),P=l(P,k,A,B,S,16,u[46]),A=h(A,B=l(B,P,k,A,d,23,u[47]),P,k,a,6,u[48]),k=h(k,A,B,P,_,10,u[49]),P=h(P,k,A,B,C,15,u[50]),B=h(B,P,k,A,g,21,u[51]),A=h(A,B,P,k,x,6,u[52]),k=h(k,A,B,P,p,10,u[53]),P=h(P,k,A,B,m,15,u[54]),B=h(B,P,k,A,s,21,u[55]),A=h(A,B,P,k,b,6,u[56]),k=h(k,A,B,P,S,10,u[57]),P=h(P,k,A,B,y,15,u[58]),B=h(B,P,k,A,O,21,u[59]),A=h(A,B,P,k,v,6,u[60]),k=h(k,A,B,P,E,10,u[61]),P=h(P,k,A,B,d,15,u[62]),B=h(B,P,k,A,w,21,u[63]),i[0]=i[0]+A|0,i[1]=i[1]+B|0,i[2]=i[2]+P|0,i[3]=i[3]+k|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,o=8*t.sigBytes;r[o>>>5]|=128<<24-o%32;var i=e.floor(n/4294967296),a=n;r[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),r[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var u=this._hash,s=u.words,c=0;c<4;c++){var f=s[c];s[c]=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8)}return u},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});function c(t,e,r,n,o,i,a){var u=t+(e&r|~e&n)+o+a;return(u<<i|u>>>32-i)+e}function f(t,e,r,n,o,i,a){var u=t+(e&n|r&~n)+o+a;return(u<<i|u>>>32-i)+e}function l(t,e,r,n,o,i,a){var u=t+(e^r^n)+o+a;return(u<<i|u>>>32-i)+e}function h(t,e,r,n,o,i,a){var u=t+(r^(e|~n))+o+a;return(u<<i|u>>>32-i)+e}r.MD5=i._createHelper(s),r.HmacMD5=i._createHmacHelper(s)}(Math),t.MD5}(f())),A.exports}var P,k={exports:{}};function M(){return P||(P=1,k.exports=function(t){return r=(e=t).lib,n=r.WordArray,o=r.Hasher,i=e.algo,a=[],u=i.SHA1=o.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],u=r[3],s=r[4],c=0;c<80;c++){if(c<16)a[c]=0|t[e+c];else{var f=a[c-3]^a[c-8]^a[c-14]^a[c-16];a[c]=f<<1|f>>>31}var l=(n<<5|n>>>27)+s+a[c];l+=c<20?1518500249+(o&i|~o&u):c<40?1859775393+(o^i^u):c<60?(o&i|o&u|i&u)-1894007588:(o^i^u)-899497514,s=u,u=i,i=o<<30|o>>>2,o=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+i|0,r[3]=r[3]+u|0,r[4]=r[4]+s|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(n+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}}),e.SHA1=o._createHelper(u),e.HmacSHA1=o._createHmacHelper(u),t.SHA1;var e,r,n,o,i,a,u}(f())),k.exports}var j,R={exports:{}};function T(){return j||(j=1,R.exports=function(t){return function(e){var r=t,n=r.lib,o=n.WordArray,i=n.Hasher,a=r.algo,u=[],s=[];!function(){function t(t){for(var r=e.sqrt(t),n=2;n<=r;n++)if(!(t%n))return!1;return!0}function r(t){return 4294967296*(t-(0|t))|0}for(var n=2,o=0;o<64;)t(n)&&(o<8&&(u[o]=r(e.pow(n,.5))),s[o]=r(e.pow(n,1/3)),o++),n++}();var c=[],f=a.SHA256=i.extend({_doReset:function(){this._hash=new o.init(u.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],a=r[3],u=r[4],f=r[5],l=r[6],h=r[7],d=0;d<64;d++){if(d<16)c[d]=0|t[e+d];else{var p=c[d-15],v=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,g=c[d-2],y=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[d]=v+c[d-7]+y+c[d-16]}var _=n&o^n&i^o&i,b=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),w=h+((u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25))+(u&f^~u&l)+s[d]+c[d];h=l,l=f,f=u,u=a+w|0,a=i,i=o,o=n,n=w+(b+_)|0}r[0]=r[0]+n|0,r[1]=r[1]+o|0,r[2]=r[2]+i|0,r[3]=r[3]+a|0,r[4]=r[4]+u|0,r[5]=r[5]+f|0,r[6]=r[6]+l|0,r[7]=r[7]+h|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,o=8*t.sigBytes;return r[o>>>5]|=128<<24-o%32,r[14+(o+64>>>9<<4)]=e.floor(n/4294967296),r[15+(o+64>>>9<<4)]=n,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});r.SHA256=i._createHelper(f),r.HmacSHA256=i._createHmacHelper(f)}(Math),t.SHA256}(f())),R.exports}var I,L={exports:{}};var H,z={exports:{}};function D(){return H||(H=1,z.exports=function(t){return function(){var e=t,r=e.lib.Hasher,n=e.x64,o=n.Word,i=n.WordArray,a=e.algo;function u(){return o.create.apply(o,arguments)}var s=[u(1116352408,3609767458),u(1899447441,602891725),u(3049323471,3964484399),u(3921009573,2173295548),u(961987163,4081628472),u(1508970993,3053834265),u(2453635748,2937671579),u(2870763221,3664609560),u(3624381080,2734883394),u(310598401,1164996542),u(607225278,1323610764),u(1426881987,3590304994),u(1925078388,4068182383),u(2162078206,991336113),u(2614888103,633803317),u(3248222580,3479774868),u(3835390401,2666613458),u(4022224774,944711139),u(264347078,2341262773),u(604807628,2007800933),u(770255983,1495990901),u(1249150122,1856431235),u(1555081692,3175218132),u(1996064986,2198950837),u(2554220882,3999719339),u(2821834349,766784016),u(2952996808,2566594879),u(3210313671,3203337956),u(3336571891,1034457026),u(3584528711,2466948901),u(113926993,3758326383),u(338241895,168717936),u(666307205,1188179964),u(773529912,1546045734),u(1294757372,1522805485),u(1396182291,2643833823),u(1695183700,2343527390),u(1986661051,1014477480),u(2177026350,1206759142),u(2456956037,344077627),u(2730485921,1290863460),u(2820302411,3158454273),u(3259730800,3505952657),u(3345764771,106217008),u(3516065817,3606008344),u(3600352804,1432725776),u(4094571909,1467031594),u(275423344,851169720),u(430227734,3100823752),u(506948616,1363258195),u(659060556,3750685593),u(883997877,3785050280),u(958139571,3318307427),u(1322822218,3812723403),u(1537002063,2003034995),u(1747873779,3602036899),u(1955562222,1575990012),u(2024104815,1125592928),u(2227730452,2716904306),u(2361852424,442776044),u(2428436474,593698344),u(2756734187,3733110249),u(3204031479,2999351573),u(3329325298,3815920427),u(3391569614,3928383900),u(3515267271,566280711),u(3940187606,3454069534),u(4118630271,4000239992),u(116418474,1914138554),u(174292421,2731055270),u(289380356,3203993006),u(460393269,320620315),u(685471733,587496836),u(852142971,1086792851),u(1017036298,365543100),u(1126000580,2618297676),u(1288033470,3409855158),u(1501505948,4234509866),u(1607167915,987167468),u(1816402316,1246189591)],c=[];!function(){for(var t=0;t<80;t++)c[t]=u()}();var f=a.SHA512=r.extend({_doReset:function(){this._hash=new i.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],o=r[1],i=r[2],a=r[3],u=r[4],f=r[5],l=r[6],h=r[7],d=n.high,p=n.low,v=o.high,g=o.low,y=i.high,_=i.low,b=a.high,w=a.low,m=u.high,E=u.low,x=f.high,O=f.low,C=l.high,S=l.low,A=h.high,B=h.low,P=d,k=p,M=v,j=g,R=y,T=_,I=b,L=w,H=m,z=E,D=x,N=O,U=C,F=S,G=A,X=B,$=0;$<80;$++){var V,K,W=c[$];if($<16)K=W.high=0|t[e+2*$],V=W.low=0|t[e+2*$+1];else{var Y=c[$-15],J=Y.high,Q=Y.low,q=(J>>>1|Q<<31)^(J>>>8|Q<<24)^J>>>7,Z=(Q>>>1|J<<31)^(Q>>>8|J<<24)^(Q>>>7|J<<25),tt=c[$-2],et=tt.high,rt=tt.low,nt=(et>>>19|rt<<13)^(et<<3|rt>>>29)^et>>>6,ot=(rt>>>19|et<<13)^(rt<<3|et>>>29)^(rt>>>6|et<<26),it=c[$-7],at=it.high,ut=it.low,st=c[$-16],ct=st.high,ft=st.low;K=(K=(K=q+at+((V=Z+ut)>>>0<Z>>>0?1:0))+nt+((V+=ot)>>>0<ot>>>0?1:0))+ct+((V+=ft)>>>0<ft>>>0?1:0),W.high=K,W.low=V}var lt,ht=H&D^~H&U,dt=z&N^~z&F,pt=P&M^P&R^M&R,vt=k&j^k&T^j&T,gt=(P>>>28|k<<4)^(P<<30|k>>>2)^(P<<25|k>>>7),yt=(k>>>28|P<<4)^(k<<30|P>>>2)^(k<<25|P>>>7),_t=(H>>>14|z<<18)^(H>>>18|z<<14)^(H<<23|z>>>9),bt=(z>>>14|H<<18)^(z>>>18|H<<14)^(z<<23|H>>>9),wt=s[$],mt=wt.high,Et=wt.low,xt=G+_t+((lt=X+bt)>>>0<X>>>0?1:0),Ot=yt+vt;G=U,X=F,U=D,F=N,D=H,N=z,H=I+(xt=(xt=(xt=xt+ht+((lt+=dt)>>>0<dt>>>0?1:0))+mt+((lt+=Et)>>>0<Et>>>0?1:0))+K+((lt+=V)>>>0<V>>>0?1:0))+((z=L+lt|0)>>>0<L>>>0?1:0)|0,I=R,L=T,R=M,T=j,M=P,j=k,P=xt+(gt+pt+(Ot>>>0<yt>>>0?1:0))+((k=lt+Ot|0)>>>0<lt>>>0?1:0)|0}p=n.low=p+k,n.high=d+P+(p>>>0<k>>>0?1:0),g=o.low=g+j,o.high=v+M+(g>>>0<j>>>0?1:0),_=i.low=_+T,i.high=y+R+(_>>>0<T>>>0?1:0),w=a.low=w+L,a.high=b+I+(w>>>0<L>>>0?1:0),E=u.low=E+z,u.high=m+H+(E>>>0<z>>>0?1:0),O=f.low=O+N,f.high=x+D+(O>>>0<N>>>0?1:0),S=l.low=S+F,l.high=C+U+(S>>>0<F>>>0?1:0),B=h.low=B+X,h.high=A+G+(B>>>0<X>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(n+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});e.SHA512=r._createHelper(f),e.HmacSHA512=r._createHmacHelper(f)}(),t.SHA512}(f(),d())),z.exports}var N,U={exports:{}};var F,G={exports:{}};function X(){return F||(F=1,G.exports=function(t){return function(e){var r=t,n=r.lib,o=n.WordArray,i=n.Hasher,a=r.x64.Word,u=r.algo,s=[],c=[],f=[];!function(){for(var t=1,e=0,r=0;r<24;r++){s[t+5*e]=(r+1)*(r+2)/2%64;var n=(2*t+3*e)%5;t=e%5,e=n}for(t=0;t<5;t++)for(e=0;e<5;e++)c[t+5*e]=e+(2*t+3*e)%5*5;for(var o=1,i=0;i<24;i++){for(var u=0,l=0,h=0;h<7;h++){if(1&o){var d=(1<<h)-1;d<32?l^=1<<d:u^=1<<d-32}128&o?o=o<<1^113:o<<=1}f[i]=a.create(u,l)}}();var l=[];!function(){for(var t=0;t<25;t++)l[t]=a.create()}();var h=u.SHA3=i.extend({cfg:i.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,n=this.blockSize/2,o=0;o<n;o++){var i=t[e+2*o],a=t[e+2*o+1];i=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(B=r[o]).high^=a,B.low^=i}for(var u=0;u<24;u++){for(var h=0;h<5;h++){for(var d=0,p=0,v=0;v<5;v++)d^=(B=r[h+5*v]).high,p^=B.low;var g=l[h];g.high=d,g.low=p}for(h=0;h<5;h++){var y=l[(h+4)%5],_=l[(h+1)%5],b=_.high,w=_.low;for(d=y.high^(b<<1|w>>>31),p=y.low^(w<<1|b>>>31),v=0;v<5;v++)(B=r[h+5*v]).high^=d,B.low^=p}for(var m=1;m<25;m++){var E=(B=r[m]).high,x=B.low,O=s[m];O<32?(d=E<<O|x>>>32-O,p=x<<O|E>>>32-O):(d=x<<O-32|E>>>64-O,p=E<<O-32|x>>>64-O);var C=l[c[m]];C.high=d,C.low=p}var S=l[0],A=r[0];for(S.high=A.high,S.low=A.low,h=0;h<5;h++)for(v=0;v<5;v++){var B=r[m=h+5*v],P=l[m],k=l[(h+1)%5+5*v],M=l[(h+2)%5+5*v];B.high=P.high^~k.high&M.high,B.low=P.low^~k.low&M.low}B=r[0];var j=f[u];B.high^=j.high,B.low^=j.low}},_doFinalize:function(){var t=this._data,r=t.words;this._nDataBytes;var n=8*t.sigBytes,i=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((n+1)/i)*i>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,u=this.cfg.outputLength/8,s=u/8,c=[],f=0;f<s;f++){var l=a[f],h=l.high,d=l.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),c.push(d),c.push(h)}return new o.init(c,u)},clone:function(){for(var t=i.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});r.SHA3=i._createHelper(h),r.HmacSHA3=i._createHmacHelper(h)}(Math),t.SHA3}(f(),d())),G.exports}var $,V={exports:{}};var K,W={exports:{}};function Y(){return K||(K=1,W.exports=function(t){var e,r,n;r=(e=t).lib.Base,n=e.enc.Utf8,e.algo.HMAC=r.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=n.parse(e));var r=t.blockSize,o=4*r;e.sigBytes>o&&(e=t.finalize(e)),e.clamp();for(var i=this._oKey=e.clone(),a=this._iKey=e.clone(),u=i.words,s=a.words,c=0;c<r;c++)u[c]^=1549556828,s[c]^=909522486;i.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,r=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(r))}})}(f())),W.exports}var J,Q={exports:{}};var q,Z={exports:{}};function tt(){return q||(q=1,Z.exports=function(t){return r=(e=t).lib,n=r.Base,o=r.WordArray,i=e.algo,a=i.MD5,u=i.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:a,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r,n=this.cfg,i=n.hasher.create(),a=o.create(),u=a.words,s=n.keySize,c=n.iterations;u.length<s;){r&&i.update(r),r=i.update(t).finalize(e),i.reset();for(var f=1;f<c;f++)r=i.finalize(r),i.reset();a.concat(r)}return a.sigBytes=4*s,a}}),e.EvpKDF=function(t,e,r){return u.create(r).compute(t,e)},t.EvpKDF;var e,r,n,o,i,a,u}(f(),M(),Y())),Z.exports}var et,rt={exports:{}};function nt(){return et||(et=1,rt.exports=function(t){t.lib.Cipher||function(e){var r=t,n=r.lib,o=n.Base,i=n.WordArray,a=n.BufferedBlockAlgorithm,u=r.enc;u.Utf8;var s=u.Base64,c=r.algo.EvpKDF,f=n.Cipher=a.extend({cfg:o.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){a.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?b:y}return function(e){return{encrypt:function(r,n,o){return t(n).encrypt(e,r,n,o)},decrypt:function(r,n,o){return t(n).decrypt(e,r,n,o)}}}}()});n.StreamCipher=f.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var l=r.mode={},h=n.BlockCipherMode=o.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),d=l.CBC=function(){var t=h.extend();function r(t,r,n){var o,i=this._iv;i?(o=i,this._iv=e):o=this._prevBlock;for(var a=0;a<n;a++)t[r+a]^=o[a]}return t.Encryptor=t.extend({processBlock:function(t,e){var n=this._cipher,o=n.blockSize;r.call(this,t,e,o),n.encryptBlock(t,e),this._prevBlock=t.slice(e,e+o)}}),t.Decryptor=t.extend({processBlock:function(t,e){var n=this._cipher,o=n.blockSize,i=t.slice(e,e+o);n.decryptBlock(t,e),r.call(this,t,e,o),this._prevBlock=i}}),t}(),p=(r.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,n=r-t.sigBytes%r,o=n<<24|n<<16|n<<8|n,a=[],u=0;u<n;u+=4)a.push(o);var s=i.create(a,n);t.concat(s)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}};n.BlockCipher=f.extend({cfg:f.cfg.extend({mode:d,padding:p}),reset:function(){var t;f.reset.call(this);var e=this.cfg,r=e.iv,n=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=n.createEncryptor:(t=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(n,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4});var v=n.CipherParams=o.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),g=(r.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,r=t.salt;return(r?i.create([1398893684,1701076831]).concat(r).concat(e):e).toString(s)},parse:function(t){var e,r=s.parse(t),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(e=i.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),v.create({ciphertext:r,salt:e})}},y=n.SerializableCipher=o.extend({cfg:o.extend({format:g}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var o=t.createEncryptor(r,n),i=o.finalize(e),a=o.cfg;return v.create({ciphertext:i,key:r,iv:a.iv,algorithm:t,mode:a.mode,padding:a.padding,blockSize:t.blockSize,formatter:n.format})},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),t.createDecryptor(r,n).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),_=(r.kdf={}).OpenSSL={execute:function(t,e,r,n,o){if(n||(n=i.random(8)),o)a=c.create({keySize:e+r,hasher:o}).compute(t,n);else var a=c.create({keySize:e+r}).compute(t,n);var u=i.create(a.words.slice(e),4*r);return a.sigBytes=4*e,v.create({key:a,iv:u,salt:n})}},b=n.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:_}),encrypt:function(t,e,r,n){var o=(n=this.cfg.extend(n)).kdf.execute(r,t.keySize,t.ivSize,n.salt,n.hasher);n.iv=o.iv;var i=y.encrypt.call(this,t,e,o.key,n);return i.mixIn(o),i},decrypt:function(t,e,r,n){n=this.cfg.extend(n),e=this._parse(e,n.format);var o=n.kdf.execute(r,t.keySize,t.ivSize,e.salt,n.hasher);return n.iv=o.iv,y.decrypt.call(this,t,e,o.key,n)}})}()}(f(),tt())),rt.exports}var ot,it={exports:{}};function at(){return ot||(ot=1,it.exports=function(t){return t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();function r(t,e,r,n){var o,i=this._iv;i?(o=i.slice(0),this._iv=void 0):o=this._prevBlock,n.encryptBlock(o,0);for(var a=0;a<r;a++)t[e+a]^=o[a]}return e.Encryptor=e.extend({processBlock:function(t,e){var n=this._cipher,o=n.blockSize;r.call(this,t,e,o,n),this._prevBlock=t.slice(e,e+o)}}),e.Decryptor=e.extend({processBlock:function(t,e){var n=this._cipher,o=n.blockSize,i=t.slice(e,e+o);r.call(this,t,e,o,n),this._prevBlock=i}}),e}(),t.mode.CFB}(f(),nt())),it.exports}var ut,st={exports:{}};function ct(){return ut||(ut=1,st.exports=function(t){return t.mode.CTR=(e=t.lib.BlockCipherMode.extend(),r=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,o=this._iv,i=this._counter;o&&(i=this._counter=o.slice(0),this._iv=void 0);var a=i.slice(0);r.encryptBlock(a,0),i[n-1]=i[n-1]+1|0;for(var u=0;u<n;u++)t[e+u]^=a[u]}}),e.Decryptor=r,e),t.mode.CTR;var e,r}(f(),nt())),st.exports}var ft,lt={exports:{}};function ht(){return ft||(ft=1,lt.exports=function(t){
/** @preserve
			 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
			 * derived from CryptoJS.mode.CTR
			 * <NAME_EMAIL>
			 */
return t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function r(t){if(255&~(t>>24))t+=1<<24;else{var e=t>>16&255,r=t>>8&255,n=255&t;255===e?(e=0,255===r?(r=0,255===n?n=0:++n):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=n}return t}function n(t){return 0===(t[0]=r(t[0]))&&(t[1]=r(t[1])),t}var o=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,o=r.blockSize,i=this._iv,a=this._counter;i&&(a=this._counter=i.slice(0),this._iv=void 0),n(a);var u=a.slice(0);r.encryptBlock(u,0);for(var s=0;s<o;s++)t[e+s]^=u[s]}});return e.Decryptor=o,e}(),t.mode.CTRGladman}(f(),nt())),lt.exports}var dt,pt={exports:{}};function vt(){return dt||(dt=1,pt.exports=function(t){return t.mode.OFB=(e=t.lib.BlockCipherMode.extend(),r=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,o=this._iv,i=this._keystream;o&&(i=this._keystream=o.slice(0),this._iv=void 0),r.encryptBlock(i,0);for(var a=0;a<n;a++)t[e+a]^=i[a]}}),e.Decryptor=r,e),t.mode.OFB;var e,r}(f(),nt())),pt.exports}var gt,yt={exports:{}};var _t,bt={exports:{}};var wt,mt={exports:{}};var Et,xt={exports:{}};var Ot,Ct={exports:{}};var St,At={exports:{}};var Bt,Pt={exports:{}};var kt,Mt={exports:{}};var jt,Rt={exports:{}};function Tt(){return jt||(jt=1,Rt.exports=function(t){return function(){var e=t,r=e.lib,n=r.WordArray,o=r.BlockCipher,i=e.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],u=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],s=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],f=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],l=i.DES=o.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;r<56;r++){var n=a[r]-1;e[r]=t[n>>>5]>>>31-n%32&1}for(var o=this._subKeys=[],i=0;i<16;i++){var c=o[i]=[],f=s[i];for(r=0;r<24;r++)c[r/6|0]|=e[(u[r]-1+f)%28]<<31-r%6,c[4+(r/6|0)]|=e[28+(u[r+24]-1+f)%28]<<31-r%6;for(c[0]=c[0]<<1|c[0]>>>31,r=1;r<7;r++)c[r]=c[r]>>>4*(r-1)+3;c[7]=c[7]<<5|c[7]>>>27}var l=this._invSubKeys=[];for(r=0;r<16;r++)l[r]=o[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],h.call(this,4,252645135),h.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),h.call(this,1,1431655765);for(var n=0;n<16;n++){for(var o=r[n],i=this._lBlock,a=this._rBlock,u=0,s=0;s<8;s++)u|=c[s][((a^o[s])&f[s])>>>0];this._lBlock=a,this._rBlock=i^u}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,h.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function d(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}e.DES=o._createHelper(l);var p=i.TripleDES=o.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),r=t.length<4?t.slice(0,2):t.slice(2,4),o=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=l.createEncryptor(n.create(e)),this._des2=l.createEncryptor(n.create(r)),this._des3=l.createEncryptor(n.create(o))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=o._createHelper(p)}(),t.TripleDES}(f(),E(),B(),tt(),nt())),Rt.exports}var It,Lt={exports:{}};var Ht,zt={exports:{}};var Dt,Nt={exports:{}};var Ut,Ft={exports:{}};function Gt(){return Ut||(Ut=1,Ft.exports=function(t){return function(){var e=t,r=e.lib.BlockCipher,n=e.algo;const o=16,i=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],a=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var u={pbox:[],sbox:[]};function s(t,e){let r=e>>24&255,n=e>>16&255,o=e>>8&255,i=255&e,a=t.sbox[0][r]+t.sbox[1][n];return a^=t.sbox[2][o],a+=t.sbox[3][i],a}function c(t,e,r){let n,i=e,a=r;for(let u=0;u<o;++u)i^=t.pbox[u],a=s(t,i)^a,n=i,i=a,a=n;return n=i,i=a,a=n,a^=t.pbox[o],i^=t.pbox[o+1],{left:i,right:a}}function f(t,e,r){let n,i=e,a=r;for(let u=o+1;u>1;--u)i^=t.pbox[u],a=s(t,i)^a,n=i,i=a,a=n;return n=i,i=a,a=n,a^=t.pbox[1],i^=t.pbox[0],{left:i,right:a}}function l(t,e,r){for(let o=0;o<4;o++){t.sbox[o]=[];for(let e=0;e<256;e++)t.sbox[o][e]=a[o][e]}let n=0;for(let a=0;a<o+2;a++)t.pbox[a]=i[a]^e[n],n++,n>=r&&(n=0);let u=0,s=0,f=0;for(let i=0;i<o+2;i+=2)f=c(t,u,s),u=f.left,s=f.right,t.pbox[i]=u,t.pbox[i+1]=s;for(let o=0;o<4;o++)for(let e=0;e<256;e+=2)f=c(t,u,s),u=f.left,s=f.right,t.sbox[o][e]=u,t.sbox[o][e+1]=s;return!0}var h=n.Blowfish=r.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4;l(u,e,r)}},encryptBlock:function(t,e){var r=c(u,t[e],t[e+1]);t[e]=r.left,t[e+1]=r.right},decryptBlock:function(t,e){var r=f(u,t[e],t[e+1]);t[e]=r.left,t[e+1]=r.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=r._createHelper(h)}(),t.Blowfish}(f(),E(),B(),tt(),nt())),Ft.exports}a.exports=function(t){return t}(f(),d(),g(),b(),E(),C(),B(),M(),T(),I||(I=1,L.exports=function(t){return r=(e=t).lib.WordArray,n=e.algo,o=n.SHA256,i=n.SHA224=o.extend({_doReset:function(){this._hash=new r.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=o._doFinalize.call(this);return t.sigBytes-=4,t}}),e.SHA224=o._createHelper(i),e.HmacSHA224=o._createHmacHelper(i),t.SHA224;var e,r,n,o,i}(f(),T())),D(),N||(N=1,U.exports=function(t){return r=(e=t).x64,n=r.Word,o=r.WordArray,i=e.algo,a=i.SHA512,u=i.SHA384=a.extend({_doReset:function(){this._hash=new o.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var t=a._doFinalize.call(this);return t.sigBytes-=16,t}}),e.SHA384=a._createHelper(u),e.HmacSHA384=a._createHmacHelper(u),t.SHA384;var e,r,n,o,i,a,u}(f(),d(),D())),X(),$||($=1,V.exports=function(t){
/** @preserve
			(c) 2012 by Cédric Mesnil. All rights reserved.

			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
			*/
return function(){var e=t,r=e.lib,n=r.WordArray,o=r.Hasher,i=e.algo,a=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),u=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),s=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),c=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),f=n.create([0,1518500249,1859775393,2400959708,2840853838]),l=n.create([1352829926,1548603684,1836072691,2053994217,0]),h=i.RIPEMD160=o.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,o=t[n];t[n]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var i,h,b,w,m,E,x,O,C,S,A,B=this._hash.words,P=f.words,k=l.words,M=a.words,j=u.words,R=s.words,T=c.words;for(E=i=B[0],x=h=B[1],O=b=B[2],C=w=B[3],S=m=B[4],r=0;r<80;r+=1)A=i+t[e+M[r]]|0,A+=r<16?d(h,b,w)+P[0]:r<32?p(h,b,w)+P[1]:r<48?v(h,b,w)+P[2]:r<64?g(h,b,w)+P[3]:y(h,b,w)+P[4],A=(A=_(A|=0,R[r]))+m|0,i=m,m=w,w=_(b,10),b=h,h=A,A=E+t[e+j[r]]|0,A+=r<16?y(x,O,C)+k[0]:r<32?g(x,O,C)+k[1]:r<48?v(x,O,C)+k[2]:r<64?p(x,O,C)+k[3]:d(x,O,C)+k[4],A=(A=_(A|=0,T[r]))+S|0,E=S,S=C,C=_(O,10),O=x,x=A;A=B[1]+b+C|0,B[1]=B[2]+w+S|0,B[2]=B[3]+m+E|0,B[3]=B[4]+i+x|0,B[4]=B[0]+h+O|0,B[0]=A},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var o=this._hash,i=o.words,a=0;a<5;a++){var u=i[a];i[a]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return o},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function d(t,e,r){return t^e^r}function p(t,e,r){return t&e|~t&r}function v(t,e,r){return(t|~e)^r}function g(t,e,r){return t&r|e&~r}function y(t,e,r){return t^(e|~r)}function _(t,e){return t<<e|t>>>32-e}e.RIPEMD160=o._createHelper(h),e.HmacRIPEMD160=o._createHmacHelper(h)}(),t.RIPEMD160}(f())),Y(),J||(J=1,Q.exports=function(t){return n=(r=(e=t).lib).Base,o=r.WordArray,a=(i=e.algo).SHA256,u=i.HMAC,s=i.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:a,iterations:25e4}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,n=u.create(r.hasher,t),i=o.create(),a=o.create([1]),s=i.words,c=a.words,f=r.keySize,l=r.iterations;s.length<f;){var h=n.update(e).finalize(a);n.reset();for(var d=h.words,p=d.length,v=h,g=1;g<l;g++){v=n.finalize(v),n.reset();for(var y=v.words,_=0;_<p;_++)d[_]^=y[_]}i.concat(h),c[0]++}return i.sigBytes=4*f,i}}),e.PBKDF2=function(t,e,r){return s.create(r).compute(t,e)},t.PBKDF2;var e,r,n,o,i,a,u,s}(f(),T(),Y())),tt(),nt(),at(),ct(),ht(),vt(),gt||(gt=1,yt.exports=function(t){return t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),e.Decryptor=e.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),e),t.mode.ECB;var e}(f(),nt())),_t||(_t=1,bt.exports=function(t){return t.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,n=4*e,o=n-r%n,i=r+o-1;t.clamp(),t.words[i>>>2]|=o<<24-i%4*8,t.sigBytes+=o},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Ansix923}(f(),nt())),wt||(wt=1,mt.exports=function(t){return t.pad.Iso10126={pad:function(e,r){var n=4*r,o=n-e.sigBytes%n;e.concat(t.lib.WordArray.random(o-1)).concat(t.lib.WordArray.create([o<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Iso10126}(f(),nt())),Et||(Et=1,xt.exports=function(t){return t.pad.Iso97971={pad:function(e,r){e.concat(t.lib.WordArray.create([2147483648],1)),t.pad.ZeroPadding.pad(e,r)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971}(f(),nt())),Ot||(Ot=1,Ct.exports=function(t){return t.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){var e=t.words,r=t.sigBytes-1;for(r=t.sigBytes-1;r>=0;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},t.pad.ZeroPadding}(f(),nt())),St||(St=1,At.exports=function(t){return t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding}(f(),nt())),Bt||(Bt=1,Pt.exports=function(t){return r=(e=t).lib.CipherParams,n=e.enc.Hex,e.format.Hex={stringify:function(t){return t.ciphertext.toString(n)},parse:function(t){var e=n.parse(t);return r.create({ciphertext:e})}},t.format.Hex;var e,r,n}(f(),nt())),kt||(kt=1,Mt.exports=function(t){return function(){var e=t,r=e.lib.BlockCipher,n=e.algo,o=[],i=[],a=[],u=[],s=[],c=[],f=[],l=[],h=[],d=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,n=0;for(e=0;e<256;e++){var p=n^n<<1^n<<2^n<<3^n<<4;p=p>>>8^255&p^99,o[r]=p,i[p]=r;var v=t[r],g=t[v],y=t[g],_=257*t[p]^16843008*p;a[r]=_<<24|_>>>8,u[r]=_<<16|_>>>16,s[r]=_<<8|_>>>24,c[r]=_,_=16843009*y^65537*g^257*v^16843008*r,f[p]=_<<24|_>>>8,l[p]=_<<16|_>>>16,h[p]=_<<8|_>>>24,d[p]=_,r?(r=v^t[t[t[y^v]]],n^=t[t[n]]):r=n=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],v=n.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,n=4*((this._nRounds=r+6)+1),i=this._keySchedule=[],a=0;a<n;a++)a<r?i[a]=e[a]:(c=i[a-1],a%r?r>6&&a%r==4&&(c=o[c>>>24]<<24|o[c>>>16&255]<<16|o[c>>>8&255]<<8|o[255&c]):(c=o[(c=c<<8|c>>>24)>>>24]<<24|o[c>>>16&255]<<16|o[c>>>8&255]<<8|o[255&c],c^=p[a/r|0]<<24),i[a]=i[a-r]^c);for(var u=this._invKeySchedule=[],s=0;s<n;s++){if(a=n-s,s%4)var c=i[a];else c=i[a-4];u[s]=s<4||a<=4?c:f[o[c>>>24]]^l[o[c>>>16&255]]^h[o[c>>>8&255]]^d[o[255&c]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,a,u,s,c,o)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,f,l,h,d,i),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,n,o,i,a,u){for(var s=this._nRounds,c=t[e]^r[0],f=t[e+1]^r[1],l=t[e+2]^r[2],h=t[e+3]^r[3],d=4,p=1;p<s;p++){var v=n[c>>>24]^o[f>>>16&255]^i[l>>>8&255]^a[255&h]^r[d++],g=n[f>>>24]^o[l>>>16&255]^i[h>>>8&255]^a[255&c]^r[d++],y=n[l>>>24]^o[h>>>16&255]^i[c>>>8&255]^a[255&f]^r[d++],_=n[h>>>24]^o[c>>>16&255]^i[f>>>8&255]^a[255&l]^r[d++];c=v,f=g,l=y,h=_}v=(u[c>>>24]<<24|u[f>>>16&255]<<16|u[l>>>8&255]<<8|u[255&h])^r[d++],g=(u[f>>>24]<<24|u[l>>>16&255]<<16|u[h>>>8&255]<<8|u[255&c])^r[d++],y=(u[l>>>24]<<24|u[h>>>16&255]<<16|u[c>>>8&255]<<8|u[255&f])^r[d++],_=(u[h>>>24]<<24|u[c>>>16&255]<<16|u[f>>>8&255]<<8|u[255&l])^r[d++],t[e]=v,t[e+1]=g,t[e+2]=y,t[e+3]=_},keySize:8});e.AES=r._createHelper(v)}(),t.AES}(f(),E(),B(),tt(),nt())),Tt(),It||(It=1,Lt.exports=function(t){return function(){var e=t,r=e.lib.StreamCipher,n=e.algo,o=n.RC4=r.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,n=this._S=[],o=0;o<256;o++)n[o]=o;o=0;for(var i=0;o<256;o++){var a=o%r,u=e[a>>>2]>>>24-a%4*8&255;i=(i+n[o]+u)%256;var s=n[o];n[o]=n[i],n[i]=s}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=i.call(this)},keySize:8,ivSize:0});function i(){for(var t=this._S,e=this._i,r=this._j,n=0,o=0;o<4;o++){r=(r+t[e=(e+1)%256])%256;var i=t[e];t[e]=t[r],t[r]=i,n|=t[(t[e]+t[r])%256]<<24-8*o}return this._i=e,this._j=r,n}e.RC4=r._createHelper(o);var a=n.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)i.call(this)}});e.RC4Drop=r._createHelper(a)}(),t.RC4}(f(),E(),B(),tt(),nt())),Ht||(Ht=1,zt.exports=function(t){return function(){var e=t,r=e.lib.StreamCipher,n=e.algo,o=[],i=[],a=[],u=n.Rabbit=r.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);var n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],o=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];for(this._b=0,r=0;r<4;r++)s.call(this);for(r=0;r<8;r++)o[r]^=n[r+4&7];if(e){var i=e.words,a=i[0],u=i[1],c=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8),l=c>>>16|4294901760&f,h=f<<16|65535&c;for(o[0]^=c,o[1]^=l,o[2]^=f,o[3]^=h,o[4]^=c,o[5]^=l,o[6]^=f,o[7]^=h,r=0;r<4;r++)s.call(this)}},_doProcessBlock:function(t,e){var r=this._X;s.call(this),o[0]=r[0]^r[5]>>>16^r[3]<<16,o[1]=r[2]^r[7]>>>16^r[5]<<16,o[2]=r[4]^r[1]>>>16^r[7]<<16,o[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)o[n]=16711935&(o[n]<<8|o[n]>>>24)|4278255360&(o[n]<<24|o[n]>>>8),t[e+n]^=o[n]},blockSize:4,ivSize:2});function s(){for(var t=this._X,e=this._C,r=0;r<8;r++)i[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<i[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<i[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<i[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<i[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<i[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<i[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<i[6]>>>0?1:0)|0,this._b=e[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var n=t[r]+e[r],o=65535&n,u=n>>>16,s=((o*o>>>17)+o*u>>>15)+u*u,c=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=s^c}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=r._createHelper(u)}(),t.Rabbit}(f(),E(),B(),tt(),nt())),Dt||(Dt=1,Nt.exports=function(t){return function(){var e=t,r=e.lib.StreamCipher,n=e.algo,o=[],i=[],a=[],u=n.RabbitLegacy=r.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var o=0;o<4;o++)s.call(this);for(o=0;o<8;o++)n[o]^=r[o+4&7];if(e){var i=e.words,a=i[0],u=i[1],c=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8),l=c>>>16|4294901760&f,h=f<<16|65535&c;for(n[0]^=c,n[1]^=l,n[2]^=f,n[3]^=h,n[4]^=c,n[5]^=l,n[6]^=f,n[7]^=h,o=0;o<4;o++)s.call(this)}},_doProcessBlock:function(t,e){var r=this._X;s.call(this),o[0]=r[0]^r[5]>>>16^r[3]<<16,o[1]=r[2]^r[7]>>>16^r[5]<<16,o[2]=r[4]^r[1]>>>16^r[7]<<16,o[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)o[n]=16711935&(o[n]<<8|o[n]>>>24)|4278255360&(o[n]<<24|o[n]>>>8),t[e+n]^=o[n]},blockSize:4,ivSize:2});function s(){for(var t=this._X,e=this._C,r=0;r<8;r++)i[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<i[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<i[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<i[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<i[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<i[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<i[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<i[6]>>>0?1:0)|0,this._b=e[7]>>>0<i[7]>>>0?1:0,r=0;r<8;r++){var n=t[r]+e[r],o=65535&n,u=n>>>16,s=((o*o>>>17)+o*u>>>15)+u*u,c=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=s^c}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=r._createHelper(u)}(),t.RabbitLegacy}(f(),E(),B(),tt(),nt())),Gt());const Xt=r(a.exports);var $t={},Vt={},Kt={};let Wt;const Yt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];Kt.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},Kt.getSymbolTotalCodewords=function(t){return Yt[t]},Kt.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},Kt.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');Wt=t},Kt.isKanjiModeEnabled=function(){return void 0!==Wt},Kt.toSJIS=function(t){return Wt(t)};var Jt,Qt={};function qt(){this.buffer=[],this.length=0}(Jt=Qt).L={bit:1},Jt.M={bit:0},Jt.Q={bit:3},Jt.H={bit:2},Jt.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},Jt.from=function(t,e){if(Jt.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return Jt.L;case"m":case"medium":return Jt.M;case"q":case"quartile":return Jt.Q;case"h":case"high":return Jt.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(r){return e}},qt.prototype={get:function(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let r=0;r<e;r++)this.putBit(1==(t>>>e-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var Zt=qt;function te(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}te.prototype.set=function(t,e,r,n){const o=t*this.size+e;this.data[o]=r,n&&(this.reservedBit[o]=!0)},te.prototype.get=function(t,e){return this.data[t*this.size+e]},te.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},te.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var ee=te,re={};!function(t){const e=Kt.getSymbolSize;t.getRowColCoords=function(t){if(1===t)return[];const r=Math.floor(t/7)+2,n=e(t),o=145===n?26:2*Math.ceil((n-13)/(2*r-2)),i=[n-7];for(let e=1;e<r-1;e++)i[e]=i[e-1]-o;return i.push(6),i.reverse()},t.getPositions=function(e){const r=[],n=t.getRowColCoords(e),o=n.length;for(let t=0;t<o;t++)for(let e=0;e<o;e++)0===t&&0===e||0===t&&e===o-1||t===o-1&&0===e||r.push([n[t],n[e]]);return r}}(re);var ne={};const oe=Kt.getSymbolSize;ne.getPositions=function(t){const e=oe(t);return[[0,0],[e-7,0],[0,e-7]]};var ie={};!function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,r=3,n=40,o=10;function i(e,r,n){switch(e){case t.Patterns.PATTERN000:return(r+n)%2==0;case t.Patterns.PATTERN001:return r%2==0;case t.Patterns.PATTERN010:return n%3==0;case t.Patterns.PATTERN011:return(r+n)%3==0;case t.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case t.Patterns.PATTERN101:return r*n%2+r*n%3==0;case t.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case t.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}t.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(t){const r=t.size;let n=0,o=0,i=0,a=null,u=null;for(let s=0;s<r;s++){o=i=0,a=u=null;for(let c=0;c<r;c++){let r=t.get(s,c);r===a?o++:(o>=5&&(n+=e+(o-5)),a=r,o=1),r=t.get(c,s),r===u?i++:(i>=5&&(n+=e+(i-5)),u=r,i=1)}o>=5&&(n+=e+(o-5)),i>=5&&(n+=e+(i-5))}return n},t.getPenaltyN2=function(t){const e=t.size;let n=0;for(let r=0;r<e-1;r++)for(let o=0;o<e-1;o++){const e=t.get(r,o)+t.get(r,o+1)+t.get(r+1,o)+t.get(r+1,o+1);4!==e&&0!==e||n++}return n*r},t.getPenaltyN3=function(t){const e=t.size;let r=0,o=0,i=0;for(let n=0;n<e;n++){o=i=0;for(let a=0;a<e;a++)o=o<<1&2047|t.get(n,a),a>=10&&(1488===o||93===o)&&r++,i=i<<1&2047|t.get(a,n),a>=10&&(1488===i||93===i)&&r++}return r*n},t.getPenaltyN4=function(t){let e=0;const r=t.data.length;for(let n=0;n<r;n++)e+=t.data[n];return Math.abs(Math.ceil(100*e/r/5)-10)*o},t.applyMask=function(t,e){const r=e.size;for(let n=0;n<r;n++)for(let o=0;o<r;o++)e.isReserved(o,n)||e.xor(o,n,i(t,o,n))},t.getBestMask=function(e,r){const n=Object.keys(t.Patterns).length;let o=0,i=1/0;for(let a=0;a<n;a++){r(a),t.applyMask(a,e);const n=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),n<i&&(i=n,o=a)}return o}}(ie);var ae={};const ue=Qt,se=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],ce=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];ae.getBlocksCount=function(t,e){switch(e){case ue.L:return se[4*(t-1)+0];case ue.M:return se[4*(t-1)+1];case ue.Q:return se[4*(t-1)+2];case ue.H:return se[4*(t-1)+3];default:return}},ae.getTotalCodewordsCount=function(t,e){switch(e){case ue.L:return ce[4*(t-1)+0];case ue.M:return ce[4*(t-1)+1];case ue.Q:return ce[4*(t-1)+2];case ue.H:return ce[4*(t-1)+3];default:return}};var fe={},le={};const he=new Uint8Array(512),de=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)he[e]=t,de[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)he[e]=he[e-255]}(),le.log=function(t){if(t<1)throw new Error("log("+t+")");return de[t]},le.exp=function(t){return he[t]},le.mul=function(t,e){return 0===t||0===e?0:he[de[t]+de[e]]},function(t){const e=le;t.mul=function(t,r){const n=new Uint8Array(t.length+r.length-1);for(let o=0;o<t.length;o++)for(let i=0;i<r.length;i++)n[o+i]^=e.mul(t[o],r[i]);return n},t.mod=function(t,r){let n=new Uint8Array(t);for(;n.length-r.length>=0;){const t=n[0];for(let i=0;i<r.length;i++)n[i]^=e.mul(r[i],t);let o=0;for(;o<n.length&&0===n[o];)o++;n=n.slice(o)}return n},t.generateECPolynomial=function(r){let n=new Uint8Array([1]);for(let o=0;o<r;o++)n=t.mul(n,new Uint8Array([1,e.exp(o)]));return n}}(fe);const pe=fe;function ve(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}ve.prototype.initialize=function(t){this.degree=t,this.genPoly=pe.generateECPolynomial(this.degree)},ve.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const r=pe.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){const t=new Uint8Array(this.degree);return t.set(r,n),t}return r};var ge=ve,ye={},_e={},be={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},we={};const me="[0-9]+";let Ee="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Ee=Ee.replace(/u/g,"\\u");const xe="(?:(?![A-Z0-9 $%*+\\-./:]|"+Ee+")(?:.|[\r\n]))+";we.KANJI=new RegExp(Ee,"g"),we.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),we.BYTE=new RegExp(xe,"g"),we.NUMERIC=new RegExp(me,"g"),we.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const Oe=new RegExp("^"+Ee+"$"),Ce=new RegExp("^"+me+"$"),Se=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");we.testKanji=function(t){return Oe.test(t)},we.testNumeric=function(t){return Ce.test(t)},we.testAlphanumeric=function(t){return Se.test(t)},function(t){const e=be,r=we;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(t,r){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!e.isValid(r))throw new Error("Invalid version: "+r);return r>=1&&r<10?t.ccBits[0]:r<27?t.ccBits[1]:t.ccBits[2]},t.getBestModeForData=function(e){return r.testNumeric(e)?t.NUMERIC:r.testAlphanumeric(e)?t.ALPHANUMERIC:r.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},t.isValid=function(t){return t&&t.bit&&t.ccBits},t.from=function(e,r){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(n){return r}}}(_e),function(t){const e=Kt,r=ae,n=Qt,o=_e,i=be,a=e.getBCHDigit(7973);function u(t,e){return o.getCharCountIndicator(t,e)+4}function s(t,e){let r=0;return t.forEach((function(t){const n=u(t.mode,e);r+=n+t.getBitsLength()})),r}t.from=function(t,e){return i.isValid(t)?parseInt(t,10):e},t.getCapacity=function(t,n,a){if(!i.isValid(t))throw new Error("Invalid QR Code version");void 0===a&&(a=o.BYTE);const s=8*(e.getSymbolTotalCodewords(t)-r.getTotalCodewordsCount(t,n));if(a===o.MIXED)return s;const c=s-u(a,t);switch(a){case o.NUMERIC:return Math.floor(c/10*3);case o.ALPHANUMERIC:return Math.floor(c/11*2);case o.KANJI:return Math.floor(c/13);case o.BYTE:default:return Math.floor(c/8)}},t.getBestVersionForData=function(e,r){let i;const a=n.from(r,n.M);if(Array.isArray(e)){if(e.length>1)return function(e,r){for(let n=1;n<=40;n++)if(s(e,n)<=t.getCapacity(n,r,o.MIXED))return n}(e,a);if(0===e.length)return 1;i=e[0]}else i=e;return function(e,r,n){for(let o=1;o<=40;o++)if(r<=t.getCapacity(o,n,e))return o}(i.mode,i.getLength(),a)},t.getEncodedBits=function(t){if(!i.isValid(t)||t<7)throw new Error("Invalid QR Code version");let r=t<<12;for(;e.getBCHDigit(r)-a>=0;)r^=7973<<e.getBCHDigit(r)-a;return t<<12|r}}(ye);var Ae={};const Be=Kt,Pe=Be.getBCHDigit(1335);Ae.getEncodedBits=function(t,e){const r=t.bit<<3|e;let n=r<<10;for(;Be.getBCHDigit(n)-Pe>=0;)n^=1335<<Be.getBCHDigit(n)-Pe;return 21522^(r<<10|n)};var ke={};const Me=_e;function je(t){this.mode=Me.NUMERIC,this.data=t.toString()}je.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},je.prototype.getLength=function(){return this.data.length},je.prototype.getBitsLength=function(){return je.getBitsLength(this.data.length)},je.prototype.write=function(t){let e,r,n;for(e=0;e+3<=this.data.length;e+=3)r=this.data.substr(e,3),n=parseInt(r,10),t.put(n,10);const o=this.data.length-e;o>0&&(r=this.data.substr(e),n=parseInt(r,10),t.put(n,3*o+1))};var Re=je;const Te=_e,Ie=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Le(t){this.mode=Te.ALPHANUMERIC,this.data=t}Le.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},Le.prototype.getLength=function(){return this.data.length},Le.prototype.getBitsLength=function(){return Le.getBitsLength(this.data.length)},Le.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*Ie.indexOf(this.data[e]);r+=Ie.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(Ie.indexOf(this.data[e]),6)};var He=Le;const ze=_e;function De(t){this.mode=ze.BYTE,this.data="string"==typeof t?(new TextEncoder).encode(t):new Uint8Array(t)}De.getBitsLength=function(t){return 8*t},De.prototype.getLength=function(){return this.data.length},De.prototype.getBitsLength=function(){return De.getBitsLength(this.data.length)},De.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)};var Ne=De;const Ue=_e,Fe=Kt;function Ge(t){this.mode=Ue.KANJI,this.data=t}Ge.getBitsLength=function(t){return 13*t},Ge.prototype.getLength=function(){return this.data.length},Ge.prototype.getBitsLength=function(){return Ge.getBitsLength(this.data.length)},Ge.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=Fe.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r-=49472}r=192*(r>>>8&255)+(255&r),t.put(r,13)}};var Xe=Ge,$e={exports:{}};!function(t){var e={single_source_shortest_paths:function(t,r,n){var o={},i={};i[r]=0;var a,u,s,c,f,l,h,d=e.PriorityQueue.make();for(d.push(r,0);!d.empty();)for(s in u=(a=d.pop()).value,c=a.cost,f=t[u]||{})f.hasOwnProperty(s)&&(l=c+f[s],h=i[s],(void 0===i[s]||h>l)&&(i[s]=l,d.push(s,l),o[s]=u));if(void 0!==n&&void 0===i[n]){var p=["Could not find a path from ",r," to ",n,"."].join("");throw new Error(p)}return o},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],n=e;n;)r.push(n),t[n],n=t[n];return r.reverse(),r},find_path:function(t,r,n){var o=e.single_source_shortest_paths(t,r,n);return e.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(t){var r,n=e.PriorityQueue,o={};for(r in t=t||{},n)n.hasOwnProperty(r)&&(o[r]=n[r]);return o.queue=[],o.sorter=t.sorter||n.default_sorter,o},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var r={value:t,cost:e};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e}($e);var Ve=$e.exports;!function(t){const e=_e,r=Re,n=He,o=Ne,i=Xe,a=we,u=Kt,s=Ve;function c(t){return unescape(encodeURIComponent(t)).length}function f(t,e,r){const n=[];let o;for(;null!==(o=t.exec(r));)n.push({data:o[0],index:o.index,mode:e,length:o[0].length});return n}function l(t){const r=f(a.NUMERIC,e.NUMERIC,t),n=f(a.ALPHANUMERIC,e.ALPHANUMERIC,t);let o,i;u.isKanjiModeEnabled()?(o=f(a.BYTE,e.BYTE,t),i=f(a.KANJI,e.KANJI,t)):(o=f(a.BYTE_KANJI,e.BYTE,t),i=[]);return r.concat(n,o,i).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function h(t,a){switch(a){case e.NUMERIC:return r.getBitsLength(t);case e.ALPHANUMERIC:return n.getBitsLength(t);case e.KANJI:return i.getBitsLength(t);case e.BYTE:return o.getBitsLength(t)}}function d(t,a){let s;const c=e.getBestModeForData(t);if(s=e.from(a,c),s!==e.BYTE&&s.bit<c.bit)throw new Error('"'+t+'" cannot be encoded with mode '+e.toString(s)+".\n Suggested mode is: "+e.toString(c));switch(s!==e.KANJI||u.isKanjiModeEnabled()||(s=e.BYTE),s){case e.NUMERIC:return new r(t);case e.ALPHANUMERIC:return new n(t);case e.KANJI:return new i(t);case e.BYTE:return new o(t)}}t.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(d(e,null)):e.data&&t.push(d(e.data,e.mode)),t}),[])},t.fromString=function(r,n){const o=function(t){const r=[];for(let n=0;n<t.length;n++){const o=t[n];switch(o.mode){case e.NUMERIC:r.push([o,{data:o.data,mode:e.ALPHANUMERIC,length:o.length},{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.ALPHANUMERIC:r.push([o,{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.KANJI:r.push([o,{data:o.data,mode:e.BYTE,length:c(o.data)}]);break;case e.BYTE:r.push([{data:o.data,mode:e.BYTE,length:c(o.data)}])}}return r}(l(r,u.isKanjiModeEnabled())),i=function(t,r){const n={},o={start:{}};let i=["start"];for(let a=0;a<t.length;a++){const u=t[a],s=[];for(let t=0;t<u.length;t++){const c=u[t],f=""+a+t;s.push(f),n[f]={node:c,lastCount:0},o[f]={};for(let t=0;t<i.length;t++){const a=i[t];n[a]&&n[a].node.mode===c.mode?(o[a][f]=h(n[a].lastCount+c.length,c.mode)-h(n[a].lastCount,c.mode),n[a].lastCount+=c.length):(n[a]&&(n[a].lastCount=c.length),o[a][f]=h(c.length,c.mode)+4+e.getCharCountIndicator(c.mode,r))}}i=s}for(let e=0;e<i.length;e++)o[i[e]].end=0;return{map:o,table:n}}(o,n),a=s.find_path(i.map,"start","end"),f=[];for(let t=1;t<a.length-1;t++)f.push(i.table[a[t]].node);return t.fromArray(function(t){return t.reduce((function(t,e){const r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(f))},t.rawSplit=function(e){return t.fromArray(l(e,u.isKanjiModeEnabled()))}}(ke);const Ke=Kt,We=Qt,Ye=Zt,Je=ee,Qe=re,qe=ne,Ze=ie,tr=ae,er=ge,rr=ye,nr=Ae,or=_e,ir=ke;function ar(t,e,r){const n=t.size,o=nr.getEncodedBits(e,r);let i,a;for(i=0;i<15;i++)a=1==(o>>i&1),i<6?t.set(i,8,a,!0):i<8?t.set(i+1,8,a,!0):t.set(n-15+i,8,a,!0),i<8?t.set(8,n-i-1,a,!0):i<9?t.set(8,15-i-1+1,a,!0):t.set(8,15-i-1,a,!0);t.set(n-8,8,1,!0)}function ur(t,e,r){const n=new Ye;r.forEach((function(e){n.put(e.mode.bit,4),n.put(e.getLength(),or.getCharCountIndicator(e.mode,t)),e.write(n)}));const o=8*(Ke.getSymbolTotalCodewords(t)-tr.getTotalCodewordsCount(t,e));for(n.getLengthInBits()+4<=o&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);const i=(o-n.getLengthInBits())/8;for(let a=0;a<i;a++)n.put(a%2?17:236,8);return function(t,e,r){const n=Ke.getSymbolTotalCodewords(e),o=tr.getTotalCodewordsCount(e,r),i=n-o,a=tr.getBlocksCount(e,r),u=n%a,s=a-u,c=Math.floor(n/a),f=Math.floor(i/a),l=f+1,h=c-f,d=new er(h);let p=0;const v=new Array(a),g=new Array(a);let y=0;const _=new Uint8Array(t.buffer);for(let x=0;x<a;x++){const t=x<s?f:l;v[x]=_.slice(p,p+t),g[x]=d.encode(v[x]),p+=t,y=Math.max(y,t)}const b=new Uint8Array(n);let w,m,E=0;for(w=0;w<y;w++)for(m=0;m<a;m++)w<v[m].length&&(b[E++]=v[m][w]);for(w=0;w<h;w++)for(m=0;m<a;m++)b[E++]=g[m][w];return b}(n,t,e)}function sr(t,e,r,n){let o;if(Array.isArray(t))o=ir.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");{let n=e;if(!n){const e=ir.rawSplit(t);n=rr.getBestVersionForData(e,r)}o=ir.fromString(t,n||40)}}const i=rr.getBestVersionForData(o,r);if(!i)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<i)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+i+".\n")}else e=i;const a=ur(e,r,o),u=Ke.getSymbolSize(e),s=new Je(u);return function(t,e){const r=t.size,n=qe.getPositions(e);for(let o=0;o<n.length;o++){const e=n[o][0],i=n[o][1];for(let n=-1;n<=7;n++)if(!(e+n<=-1||r<=e+n))for(let o=-1;o<=7;o++)i+o<=-1||r<=i+o||(n>=0&&n<=6&&(0===o||6===o)||o>=0&&o<=6&&(0===n||6===n)||n>=2&&n<=4&&o>=2&&o<=4?t.set(e+n,i+o,!0,!0):t.set(e+n,i+o,!1,!0))}}(s,e),function(t){const e=t.size;for(let r=8;r<e-8;r++){const e=r%2==0;t.set(r,6,e,!0),t.set(6,r,e,!0)}}(s),function(t,e){const r=Qe.getPositions(e);for(let n=0;n<r.length;n++){const e=r[n][0],o=r[n][1];for(let r=-2;r<=2;r++)for(let n=-2;n<=2;n++)-2===r||2===r||-2===n||2===n||0===r&&0===n?t.set(e+r,o+n,!0,!0):t.set(e+r,o+n,!1,!0)}}(s,e),ar(s,r,0),e>=7&&function(t,e){const r=t.size,n=rr.getEncodedBits(e);let o,i,a;for(let u=0;u<18;u++)o=Math.floor(u/3),i=u%3+r-8-3,a=1==(n>>u&1),t.set(o,i,a,!0),t.set(i,o,a,!0)}(s,e),function(t,e){const r=t.size;let n=-1,o=r-1,i=7,a=0;for(let u=r-1;u>0;u-=2)for(6===u&&u--;;){for(let r=0;r<2;r++)if(!t.isReserved(o,u-r)){let n=!1;a<e.length&&(n=1==(e[a]>>>i&1)),t.set(o,u-r,n),i--,-1===i&&(a++,i=7)}if(o+=n,o<0||r<=o){o-=n,n=-n;break}}}(s,a),isNaN(n)&&(n=Ze.getBestMask(s,ar.bind(null,s,r))),Ze.applyMask(n,s),ar(s,r,n),{modules:s,version:e,errorCorrectionLevel:r,maskPattern:n,segments:o}}Vt.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");let r,n,o=We.M;return void 0!==e&&(o=We.from(e.errorCorrectionLevel,We.M),r=rr.from(e.version),n=Ze.from(e.maskPattern),e.toSJISFunc&&Ke.setToSJISFunction(e.toSJISFunc)),sr(t,r,o,n)};var cr={},fr={};!function(t){function e(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}t.getOptions=function(t){t||(t={}),t.color||(t.color={});const r=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,n=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:n,scale:n?4:o,margin:r,color:{dark:e(t.color.dark||"#000000ff"),light:e(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},t.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},t.getImageWidth=function(e,r){const n=t.getScale(e,r);return Math.floor((e+2*r.margin)*n)},t.qrToImageData=function(e,r,n){const o=r.modules.size,i=r.modules.data,a=t.getScale(o,n),u=Math.floor((o+2*n.margin)*a),s=n.margin*a,c=[n.color.light,n.color.dark];for(let t=0;t<u;t++)for(let r=0;r<u;r++){let f=4*(t*u+r),l=n.color.light;if(t>=s&&r>=s&&t<u-s&&r<u-s){l=c[i[Math.floor((t-s)/a)*o+Math.floor((r-s)/a)]?1:0]}e[f++]=l.r,e[f++]=l.g,e[f++]=l.b,e[f]=l.a}}}(fr),function(t){const e=fr;t.render=function(t,r,n){let o=n,i=r;void 0!==o||r&&r.getContext||(o=r,r=void 0),r||(i=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),o=e.getOptions(o);const a=e.getImageWidth(t.modules.size,o),u=i.getContext("2d"),s=u.createImageData(a,a);return e.qrToImageData(s.data,t,o),function(t,e,r){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=r,e.width=r,e.style.height=r+"px",e.style.width=r+"px"}(u,i,a),u.putImageData(s,0,0),i},t.renderToDataURL=function(e,r,n){let o=n;void 0!==o||r&&r.getContext||(o=r,r=void 0),o||(o={});const i=t.render(e,r,o),a=o.type||"image/png",u=o.rendererOpts||{};return i.toDataURL(a,u.quality)}}(cr);var lr={};const hr=fr;function dr(t,e){const r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function pr(t,e,r){let n=t+e;return void 0!==r&&(n+=" "+r),n}lr.render=function(t,e,r){const n=hr.getOptions(e),o=t.modules.size,i=t.modules.data,a=o+2*n.margin,u=n.color.light.a?"<path "+dr(n.color.light,"fill")+' d="M0 0h'+a+"v"+a+'H0z"/>':"",s="<path "+dr(n.color.dark,"stroke")+' d="'+function(t,e,r){let n="",o=0,i=!1,a=0;for(let u=0;u<t.length;u++){const s=Math.floor(u%e),c=Math.floor(u/e);s||i||(i=!0),t[u]?(a++,u>0&&s>0&&t[u-1]||(n+=i?pr("M",s+r,.5+c+r):pr("m",o,0),o=0,i=!1),s+1<e&&t[u+1]||(n+=pr("h",a),a=0)):o++}return n}(i,o,n.margin)+'"/>',c='viewBox="0 0 '+a+" "+a+'"',f='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+c+' shape-rendering="crispEdges">'+u+s+"</svg>\n";return"function"==typeof r&&r(null,f),f};const vr=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},gr=Vt,yr=cr,_r=lr;function br(t,e,r,n,o){const i=[].slice.call(arguments,1),a=i.length,u="function"==typeof i[a-1];if(!u&&!vr())throw new Error("Callback required as last argument");if(!u){if(a<1)throw new Error("Too few arguments provided");return 1===a?(r=e,e=n=void 0):2!==a||e.getContext||(n=r,r=e,e=void 0),new Promise((function(o,i){try{const i=gr.create(r,n);o(t(i,e,n))}catch(a){i(a)}}))}if(a<2)throw new Error("Too few arguments provided");2===a?(o=r,r=e,e=n=void 0):3===a&&(e.getContext&&void 0===o?(o=n,n=void 0):(o=n,n=r,r=e,e=void 0));try{const i=gr.create(r,n);o(null,t(i,e,n))}catch(s){o(s)}}$t.create=gr.create,$t.toCanvas=br.bind(null,yr.render),$t.toDataURL=br.bind(null,yr.renderToDataURL),$t.toString=br.bind(null,(function(t,e,r){return _r.render(t,r)}));var wr={},mr={},Er={};Object.defineProperty(Er,"__esModule",{value:!0});Er.default=function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.data=e,this.text=r.text||e,this.options=r},Object.defineProperty(mr,"__esModule",{value:!0}),mr.CODE39=void 0;var xr,Or=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Cr=(xr=Er)&&xr.__esModule?xr:{default:xr};var Sr=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e=e.toUpperCase(),r.mod43&&(e+=function(t){return Ar[t]}(function(t){for(var e=0,r=0;r<t.length;r++)e+=kr(t[r]);return e%=43,e}(e))),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Cr.default),Or(t,[{key:"encode",value:function(){for(var t=Pr("*"),e=0;e<this.data.length;e++)t+=Pr(this.data[e])+"0";return{data:t+=Pr("*"),text:this.text}}},{key:"valid",value:function(){return-1!==this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)}}]),t}(),Ar=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","*"],Br=[20957,29783,23639,30485,20951,29813,23669,20855,29789,23645,29975,23831,30533,22295,30149,24005,21623,29981,23837,22301,30023,23879,30545,22343,30161,24017,21959,30065,23921,22385,29015,18263,29141,17879,29045,18293,17783,29021,18269,17477,17489,17681,20753,35770];function Pr(t){return function(t){return Br[t].toString(2)}(kr(t))}function kr(t){return Ar.indexOf(t)}mr.CODE39=Sr;var Mr,jr={},Rr={},Tr={},Ir={};function Lr(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Object.defineProperty(Ir,"__esModule",{value:!0});var Hr=Ir.SET_A=0,zr=Ir.SET_B=1,Dr=Ir.SET_C=2;Ir.SHIFT=98;var Nr=Ir.START_A=103,Ur=Ir.START_B=104,Fr=Ir.START_C=105;Ir.MODULO=103,Ir.STOP=106,Ir.FNC1=207,Ir.SET_BY_CODE=(Lr(Mr={},Nr,Hr),Lr(Mr,Ur,zr),Lr(Mr,Fr,Dr),Mr),Ir.SWAP={101:Hr,100:zr,99:Dr},Ir.A_START_CHAR=String.fromCharCode(208),Ir.B_START_CHAR=String.fromCharCode(209),Ir.C_START_CHAR=String.fromCharCode(210),Ir.A_CHARS="[\0-_È-Ï]",Ir.B_CHARS="[ -È-Ï]",Ir.C_CHARS="(Ï*[0-9]{2}Ï*)",Ir.BARS=[11011001100,11001101100,11001100110,10010011e3,10010001100,10001001100,10011001e3,10011000100,10001100100,11001001e3,11001000100,11000100100,10110011100,10011011100,10011001110,10111001100,10011101100,10011100110,11001110010,11001011100,11001001110,11011100100,11001110100,11101101110,11101001100,11100101100,11100100110,11101100100,11100110100,11100110010,11011011e3,11011000110,11000110110,10100011e3,10001011e3,10001000110,10110001e3,10001101e3,10001100010,11010001e3,11000101e3,11000100010,10110111e3,10110001110,10001101110,10111011e3,10111000110,10001110110,11101110110,11010001110,11000101110,11011101e3,11011100010,11011101110,11101011e3,11101000110,11100010110,11101101e3,11101100010,11100011010,11101111010,11001000010,11110001010,1010011e4,10100001100,1001011e4,10010000110,10000101100,10000100110,1011001e4,10110000100,1001101e4,10011000010,10000110100,10000110010,11000010010,1100101e4,11110111010,11000010100,10001111010,10100111100,10010111100,10010011110,10111100100,10011110100,10011110010,11110100100,11110010100,11110010010,11011011110,11011110110,11110110110,10101111e3,10100011110,10001011110,10111101e3,10111100010,11110101e3,11110100010,10111011110,10111101110,11101011110,11110101110,11010000100,1101001e4,11010011100,1100011101011],Object.defineProperty(Tr,"__esModule",{value:!0});var Gr=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Xr=function(t){return t&&t.__esModule?t:{default:t}}(Er),$r=Ir;var Vr=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e.substring(1),r));return n.bytes=e.split("").map((function(t){return t.charCodeAt(0)})),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Xr.default),Gr(t,[{key:"valid",value:function(){return/^[\x00-\x7F\xC8-\xD3]+$/.test(this.data)}},{key:"encode",value:function(){var e=this.bytes,r=e.shift()-105,n=$r.SET_BY_CODE[r];if(void 0===n)throw new RangeError("The encoding does not start with a start character.");!0===this.shouldEncodeAsEan128()&&e.unshift($r.FNC1);var o=t.next(e,1,n);return{text:this.text===this.data?this.text.replace(/[^\x20-\x7E]/g,""):this.text,data:t.getBar(r)+o.result+t.getBar((o.checksum+r)%$r.MODULO)+t.getBar($r.STOP)}}},{key:"shouldEncodeAsEan128",value:function(){var t=this.options.ean128||!1;return"string"==typeof t&&(t="true"===t.toLowerCase()),t}}],[{key:"getBar",value:function(t){return $r.BARS[t]?$r.BARS[t].toString():""}},{key:"correctIndex",value:function(t,e){if(e===$r.SET_A){var r=t.shift();return r<32?r+64:r-32}return e===$r.SET_B?t.shift()-32:10*(t.shift()-48)+t.shift()-48}},{key:"next",value:function(e,r,n){if(!e.length)return{result:"",checksum:0};var o=void 0,i=void 0;if(e[0]>=200){i=e.shift()-105;var a=$r.SWAP[i];void 0!==a?o=t.next(e,r+1,a):(n!==$r.SET_A&&n!==$r.SET_B||i!==$r.SHIFT||(e[0]=n===$r.SET_A?e[0]>95?e[0]-96:e[0]:e[0]<32?e[0]+96:e[0]),o=t.next(e,r+1,n))}else i=t.correctIndex(e,n),o=t.next(e,r+1,n);var u=i*r;return{result:t.getBar(i)+o.result,checksum:u+o.checksum}}}]),t}();Tr.default=Vr;var Kr={};Object.defineProperty(Kr,"__esModule",{value:!0});var Wr=Ir,Yr=function(t){return t.match(new RegExp("^"+Wr.A_CHARS+"*"))[0].length},Jr=function(t){return t.match(new RegExp("^"+Wr.B_CHARS+"*"))[0].length},Qr=function(t){return t.match(new RegExp("^"+Wr.C_CHARS+"*"))[0]};function qr(t,e){var r=e?Wr.A_CHARS:Wr.B_CHARS,n=t.match(new RegExp("^("+r+"+?)(([0-9]{2}){2,})([^0-9]|$)"));if(n)return n[1]+String.fromCharCode(204)+Zr(t.substring(n[1].length));var o=t.match(new RegExp("^"+r+"+"))[0];return o.length===t.length?t:o+String.fromCharCode(e?205:206)+qr(t.substring(o.length),!e)}function Zr(t){var e=Qr(t),r=e.length;if(r===t.length)return t;t=t.substring(r);var n=Yr(t)>=Jr(t);return e+String.fromCharCode(n?206:205)+qr(t,n)}Kr.default=function(t){var e=void 0;if(Qr(t).length>=2)e=Wr.C_START_CHAR+Zr(t);else{var r=Yr(t)>Jr(t);e=(r?Wr.A_START_CHAR:Wr.B_START_CHAR)+qr(t,r)}return e.replace(/[\xCD\xCE]([^])[\xCD\xCE]/,(function(t,e){return String.fromCharCode(203)+e}))},Object.defineProperty(Rr,"__esModule",{value:!0});var tn=rn(Tr),en=rn(Kr);function rn(t){return t&&t.__esModule?t:{default:t}}function nn(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var on=function(){function t(e,r){if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),/^[\x00-\x7F\xC8-\xD3]+$/.test(e))var n=nn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,(0,en.default)(e),r));else n=nn(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return nn(n)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,tn.default),t}();Rr.default=on;var an={};Object.defineProperty(an,"__esModule",{value:!0});var un=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),sn=function(t){return t&&t.__esModule?t:{default:t}}(Tr),cn=Ir;var fn=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,cn.A_START_CHAR+e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,sn.default),un(t,[{key:"valid",value:function(){return new RegExp("^"+cn.A_CHARS+"+$").test(this.data)}}]),t}();an.default=fn;var ln={};Object.defineProperty(ln,"__esModule",{value:!0});var hn=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),dn=function(t){return t&&t.__esModule?t:{default:t}}(Tr),pn=Ir;var vn=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,pn.B_START_CHAR+e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,dn.default),hn(t,[{key:"valid",value:function(){return new RegExp("^"+pn.B_CHARS+"+$").test(this.data)}}]),t}();ln.default=vn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0});var yn=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),_n=function(t){return t&&t.__esModule?t:{default:t}}(Tr),bn=Ir;var wn=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,bn.C_START_CHAR+e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,_n.default),yn(t,[{key:"valid",value:function(){return new RegExp("^"+bn.C_CHARS+"+$").test(this.data)}}]),t}();gn.default=wn,Object.defineProperty(jr,"__esModule",{value:!0}),jr.CODE128C=jr.CODE128B=jr.CODE128A=jr.CODE128=void 0;var mn=Cn(Rr),En=Cn(an),xn=Cn(ln),On=Cn(gn);function Cn(t){return t&&t.__esModule?t:{default:t}}jr.CODE128=mn.default,jr.CODE128A=En.default,jr.CODE128B=xn.default,jr.CODE128C=On.default;var Sn={},An={},Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.SIDE_BIN="101",Bn.MIDDLE_BIN="01010",Bn.BINARIES={L:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],G:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],R:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"],O:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],E:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"]},Bn.EAN2_STRUCTURE=["LL","LG","GL","GG"],Bn.EAN5_STRUCTURE=["GGLLL","GLGLL","GLLGL","GLLLG","LGGLL","LLGGL","LLLGG","LGLGL","LGLLG","LLGLG"],Bn.EAN13_STRUCTURE=["LLLLLL","LLGLGG","LLGGLG","LLGGGL","LGLLGG","LGGLLG","LGGGLL","LGLGLG","LGLGGL","LGGLGL"];var Pn={},kn={};Object.defineProperty(kn,"__esModule",{value:!0});var Mn=Bn;kn.default=function(t,e,r){var n=t.split("").map((function(t,r){return Mn.BINARIES[e[r]]})).map((function(e,r){return e?e[t[r]]:""}));if(r){var o=t.length-1;n=n.map((function(t,e){return e<o?t+r:t}))}return n.join("")},Object.defineProperty(Pn,"__esModule",{value:!0});var jn=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Rn=Bn,Tn=Ln(kn),In=Ln(Er);function Ln(t){return t&&t.__esModule?t:{default:t}}var Hn=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return n.fontSize=!r.flat&&r.fontSize>10*r.width?10*r.width:r.fontSize,n.guardHeight=r.height+n.fontSize/2+r.textMargin,n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,In.default),jn(t,[{key:"encode",value:function(){return this.options.flat?this.encodeFlat():this.encodeGuarded()}},{key:"leftText",value:function(t,e){return this.text.substr(t,e)}},{key:"leftEncode",value:function(t,e){return(0,Tn.default)(t,e)}},{key:"rightText",value:function(t,e){return this.text.substr(t,e)}},{key:"rightEncode",value:function(t,e){return(0,Tn.default)(t,e)}},{key:"encodeGuarded",value:function(){var t={fontSize:this.fontSize},e={height:this.guardHeight};return[{data:Rn.SIDE_BIN,options:e},{data:this.leftEncode(),text:this.leftText(),options:t},{data:Rn.MIDDLE_BIN,options:e},{data:this.rightEncode(),text:this.rightText(),options:t},{data:Rn.SIDE_BIN,options:e}]}},{key:"encodeFlat",value:function(){return{data:[Rn.SIDE_BIN,this.leftEncode(),Rn.MIDDLE_BIN,this.rightEncode(),Rn.SIDE_BIN].join(""),text:this.text}}}]),t}();Pn.default=Hn,Object.defineProperty(An,"__esModule",{value:!0});var zn=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Dn=function t(e,r,n){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,r);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,r,n)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(n):void 0},Nn=Bn,Un=function(t){return t&&t.__esModule?t:{default:t}}(Pn);var Fn=function(t){return(10-t.substr(0,12).split("").map((function(t){return+t})).reduce((function(t,e,r){return r%2?t+3*e:t+e}),0)%10)%10},Gn=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),-1!==e.search(/^[0-9]{12}$/)&&(e+=Fn(e));var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return n.lastChar=r.lastChar,n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Un.default),zn(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{13}$/)&&+this.data[12]===Fn(this.data)}},{key:"leftText",value:function(){return Dn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"leftText",this).call(this,1,6)}},{key:"leftEncode",value:function(){var e=this.data.substr(1,6),r=Nn.EAN13_STRUCTURE[this.data[0]];return Dn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"leftEncode",this).call(this,e,r)}},{key:"rightText",value:function(){return Dn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"rightText",this).call(this,7,6)}},{key:"rightEncode",value:function(){var e=this.data.substr(7,6);return Dn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"rightEncode",this).call(this,e,"RRRRRR")}},{key:"encodeGuarded",value:function(){var e=Dn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"encodeGuarded",this).call(this);return this.options.displayValue&&(e.unshift({data:"000000000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),this.options.lastChar&&(e.push({data:"00"}),e.push({data:"00000",text:this.options.lastChar,options:{fontSize:this.fontSize}}))),e}}]),t}();An.default=Gn;var Xn={};Object.defineProperty(Xn,"__esModule",{value:!0});var $n=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Vn=function t(e,r,n){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,r);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,r,n)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(n):void 0},Kn=function(t){return t&&t.__esModule?t:{default:t}}(Pn);var Wn=function(t){return(10-t.substr(0,7).split("").map((function(t){return+t})).reduce((function(t,e,r){return r%2?t+e:t+3*e}),0)%10)%10},Yn=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),-1!==e.search(/^[0-9]{7}$/)&&(e+=Wn(e)),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Kn.default),$n(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{8}$/)&&+this.data[7]===Wn(this.data)}},{key:"leftText",value:function(){return Vn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"leftText",this).call(this,0,4)}},{key:"leftEncode",value:function(){var e=this.data.substr(0,4);return Vn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"leftEncode",this).call(this,e,"LLLL")}},{key:"rightText",value:function(){return Vn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"rightText",this).call(this,4,4)}},{key:"rightEncode",value:function(){var e=this.data.substr(4,4);return Vn(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"rightEncode",this).call(this,e,"RRRR")}}]),t}();Xn.default=Yn;var Jn={};Object.defineProperty(Jn,"__esModule",{value:!0});var Qn=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),qn=Bn,Zn=eo(kn),to=eo(Er);function eo(t){return t&&t.__esModule?t:{default:t}}var ro=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,to.default),Qn(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{5}$/)}},{key:"encode",value:function(){var t,e=qn.EAN5_STRUCTURE[(t=this.data,t.split("").map((function(t){return+t})).reduce((function(t,e,r){return r%2?t+9*e:t+3*e}),0)%10)];return{data:"1011"+(0,Zn.default)(this.data,e,"01"),text:this.text}}}]),t}();Jn.default=ro;var no={};Object.defineProperty(no,"__esModule",{value:!0});var oo=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),io=Bn,ao=so(kn),uo=so(Er);function so(t){return t&&t.__esModule?t:{default:t}}var co=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,uo.default),oo(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{2}$/)}},{key:"encode",value:function(){var t=io.EAN2_STRUCTURE[parseInt(this.data)%4];return{data:"1011"+(0,ao.default)(this.data,t,"01"),text:this.text}}}]),t}();no.default=co;var fo={};Object.defineProperty(fo,"__esModule",{value:!0});var lo=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}();fo.checksum=yo;var ho=vo(kn),po=vo(Er);function vo(t){return t&&t.__esModule?t:{default:t}}var go=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),-1!==e.search(/^[0-9]{11}$/)&&(e+=yo(e));var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return n.displayValue=r.displayValue,r.fontSize>10*r.width?n.fontSize=10*r.width:n.fontSize=r.fontSize,n.guardHeight=r.height+n.fontSize/2+r.textMargin,n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,po.default),lo(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{12}$/)&&this.data[11]==yo(this.data)}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var t="";return t+="101",t+=(0,ho.default)(this.data.substr(0,6),"LLLLLL"),t+="01010",t+=(0,ho.default)(this.data.substr(6,6),"RRRRRR"),{data:t+="101",text:this.text}}},{key:"guardedEncoding",value:function(){var t=[];return this.displayValue&&t.push({data:"00000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),t.push({data:"101"+(0,ho.default)(this.data[0],"L"),options:{height:this.guardHeight}}),t.push({data:(0,ho.default)(this.data.substr(1,5),"LLLLL"),text:this.text.substr(1,5),options:{fontSize:this.fontSize}}),t.push({data:"01010",options:{height:this.guardHeight}}),t.push({data:(0,ho.default)(this.data.substr(6,5),"RRRRR"),text:this.text.substr(6,5),options:{fontSize:this.fontSize}}),t.push({data:(0,ho.default)(this.data[11],"R")+"101",options:{height:this.guardHeight}}),this.displayValue&&t.push({data:"00000000",text:this.text.substr(11,1),options:{textAlign:"right",fontSize:this.fontSize}}),t}}]),t}();function yo(t){var e,r=0;for(e=1;e<11;e+=2)r+=parseInt(t[e]);for(e=0;e<11;e+=2)r+=3*parseInt(t[e]);return(10-r%10)%10}fo.default=go;var _o={};Object.defineProperty(_o,"__esModule",{value:!0});var bo=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),wo=xo(kn),mo=xo(Er),Eo=fo;function xo(t){return t&&t.__esModule?t:{default:t}}function Oo(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var Co=["XX00000XXX","XX10000XXX","XX20000XXX","XXX00000XX","XXXX00000X","XXXXX00005","XXXXX00006","XXXXX00007","XXXXX00008","XXXXX00009"],So=[["EEEOOO","OOOEEE"],["EEOEOO","OOEOEE"],["EEOOEO","OOEEOE"],["EEOOOE","OOEEEO"],["EOEEOO","OEOOEE"],["EOOEEO","OEEOOE"],["EOOOEE","OEEEOO"],["EOEOEO","OEOEOE"],["EOEOOE","OEOEEO"],["EOOEOE","OEEOEO"]],Ao=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=Oo(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));if(n.isValid=!1,-1!==e.search(/^[0-9]{6}$/))n.middleDigits=e,n.upcA=Bo(e,"0"),n.text=r.text||""+n.upcA[0]+e+n.upcA[n.upcA.length-1],n.isValid=!0;else{if(-1===e.search(/^[01][0-9]{7}$/))return Oo(n);if(n.middleDigits=e.substring(1,e.length-1),n.upcA=Bo(n.middleDigits,e[0]),n.upcA[n.upcA.length-1]!==e[e.length-1])return Oo(n);n.isValid=!0}return n.displayValue=r.displayValue,r.fontSize>10*r.width?n.fontSize=10*r.width:n.fontSize=r.fontSize,n.guardHeight=r.height+n.fontSize/2+r.textMargin,n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,mo.default),bo(t,[{key:"valid",value:function(){return this.isValid}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var t="";return t+="101",t+=this.encodeMiddleDigits(),{data:t+="010101",text:this.text}}},{key:"guardedEncoding",value:function(){var t=[];return this.displayValue&&t.push({data:"00000000",text:this.text[0],options:{textAlign:"left",fontSize:this.fontSize}}),t.push({data:"101",options:{height:this.guardHeight}}),t.push({data:this.encodeMiddleDigits(),text:this.text.substring(1,7),options:{fontSize:this.fontSize}}),t.push({data:"010101",options:{height:this.guardHeight}}),this.displayValue&&t.push({data:"00000000",text:this.text[7],options:{textAlign:"right",fontSize:this.fontSize}}),t}},{key:"encodeMiddleDigits",value:function(){var t=this.upcA[0],e=this.upcA[this.upcA.length-1],r=So[parseInt(e)][parseInt(t)];return(0,wo.default)(this.middleDigits,r)}}]),t}();function Bo(t,e){for(var r=parseInt(t[t.length-1]),n=Co[r],o="",i=0,a=0;a<n.length;a++){var u=n[a];o+="X"===u?t[i++]:u}return""+(o=""+e+o)+(0,Eo.checksum)(o)}_o.default=Ao,Object.defineProperty(Sn,"__esModule",{value:!0}),Sn.UPCE=Sn.UPC=Sn.EAN2=Sn.EAN5=Sn.EAN8=Sn.EAN13=void 0;var Po=Io(An),ko=Io(Xn),Mo=Io(Jn),jo=Io(no),Ro=Io(fo),To=Io(_o);function Io(t){return t&&t.__esModule?t:{default:t}}Sn.EAN13=Po.default,Sn.EAN8=ko.default,Sn.EAN5=Mo.default,Sn.EAN2=jo.default,Sn.UPC=Ro.default,Sn.UPCE=To.default;var Lo={},Ho={},zo={};Object.defineProperty(zo,"__esModule",{value:!0}),zo.START_BIN="1010",zo.END_BIN="11101",zo.BINARIES=["00110","10001","01001","11000","00101","10100","01100","00011","10010","01010"],Object.defineProperty(Ho,"__esModule",{value:!0});var Do=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),No=zo,Uo=function(t){return t&&t.__esModule?t:{default:t}}(Er);var Fo=function(){function t(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Uo.default),Do(t,[{key:"valid",value:function(){return-1!==this.data.search(/^([0-9]{2})+$/)}},{key:"encode",value:function(){var t=this,e=this.data.match(/.{2}/g).map((function(e){return t.encodePair(e)})).join("");return{data:No.START_BIN+e+No.END_BIN,text:this.text}}},{key:"encodePair",value:function(t){var e=No.BINARIES[t[1]];return No.BINARIES[t[0]].split("").map((function(t,r){return("1"===t?"111":"1")+("1"===e[r]?"000":"0")})).join("")}}]),t}();Ho.default=Fo;var Go={};Object.defineProperty(Go,"__esModule",{value:!0});var Xo=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),$o=function(t){return t&&t.__esModule?t:{default:t}}(Ho);var Vo=function(t){var e=t.substr(0,13).split("").map((function(t){return parseInt(t,10)})).reduce((function(t,e,r){return t+e*(3-r%2*2)}),0);return 10*Math.ceil(e/10)-e},Ko=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),-1!==e.search(/^[0-9]{13}$/)&&(e+=Vo(e)),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,$o.default),Xo(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]{14}$/)&&+this.data[13]===Vo(this.data)}}]),t}();Go.default=Ko,Object.defineProperty(Lo,"__esModule",{value:!0}),Lo.ITF14=Lo.ITF=void 0;var Wo=Jo(Ho),Yo=Jo(Go);function Jo(t){return t&&t.__esModule?t:{default:t}}Lo.ITF=Wo.default,Lo.ITF14=Yo.default;var Qo={},qo={};Object.defineProperty(qo,"__esModule",{value:!0});var Zo=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),ti=function(t){return t&&t.__esModule?t:{default:t}}(Er);var ei=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,ti.default),Zo(t,[{key:"encode",value:function(){for(var t="110",e=0;e<this.data.length;e++){var r=parseInt(this.data[e]).toString(2);r=ri(r,4-r.length);for(var n=0;n<r.length;n++)t+="0"==r[n]?"100":"110"}return{data:t+="1001",text:this.text}}},{key:"valid",value:function(){return-1!==this.data.search(/^[0-9]+$/)}}]),t}();function ri(t,e){for(var r=0;r<e;r++)t="0"+t;return t}qo.default=ei;var ni={},oi={};Object.defineProperty(oi,"__esModule",{value:!0}),oi.mod10=function(t){for(var e=0,r=0;r<t.length;r++){var n=parseInt(t[r]);(r+t.length)%2==0?e+=n:e+=2*n%10+Math.floor(2*n/10)}return(10-e%10)%10},oi.mod11=function(t){for(var e=0,r=[2,3,4,5,6,7],n=0;n<t.length;n++){var o=parseInt(t[t.length-1-n]);e+=r[n%r.length]*o}return(11-e%11)%11},Object.defineProperty(ni,"__esModule",{value:!0});var ii=function(t){return t&&t.__esModule?t:{default:t}}(qo),ai=oi;var ui=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e+(0,ai.mod10)(e),r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,ii.default),t}();ni.default=ui;var si={};Object.defineProperty(si,"__esModule",{value:!0});var ci=function(t){return t&&t.__esModule?t:{default:t}}(qo),fi=oi;var li=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e+(0,fi.mod11)(e),r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,ci.default),t}();si.default=li;var hi={};Object.defineProperty(hi,"__esModule",{value:!0});var di=function(t){return t&&t.__esModule?t:{default:t}}(qo),pi=oi;var vi=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e+=(0,pi.mod10)(e),e+=(0,pi.mod10)(e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,di.default),t}();hi.default=vi;var gi={};Object.defineProperty(gi,"__esModule",{value:!0});var yi=function(t){return t&&t.__esModule?t:{default:t}}(qo),_i=oi;var bi=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e+=(0,_i.mod11)(e),e+=(0,_i.mod10)(e),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,yi.default),t}();gi.default=bi,Object.defineProperty(Qo,"__esModule",{value:!0}),Qo.MSI1110=Qo.MSI1010=Qo.MSI11=Qo.MSI10=Qo.MSI=void 0;var wi=Ci(qo),mi=Ci(ni),Ei=Ci(si),xi=Ci(hi),Oi=Ci(gi);function Ci(t){return t&&t.__esModule?t:{default:t}}Qo.MSI=wi.default,Qo.MSI10=mi.default,Qo.MSI11=Ei.default,Qo.MSI1010=xi.default,Qo.MSI1110=Oi.default;var Si={};Object.defineProperty(Si,"__esModule",{value:!0}),Si.pharmacode=void 0;var Ai=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Bi=function(t){return t&&t.__esModule?t:{default:t}}(Er);var Pi=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return n.number=parseInt(e,10),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Bi.default),Ai(t,[{key:"encode",value:function(){for(var t=this.number,e="";!isNaN(t)&&0!=t;)t%2==0?(e="11100"+e,t=(t-2)/2):(e="100"+e,t=(t-1)/2);return{data:e=e.slice(0,-2),text:this.text}}},{key:"valid",value:function(){return this.number>=3&&this.number<=131070}}]),t}();Si.pharmacode=Pi;var ki={};Object.defineProperty(ki,"__esModule",{value:!0}),ki.codabar=void 0;var Mi=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),ji=function(t){return t&&t.__esModule?t:{default:t}}(Er);var Ri=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),0===e.search(/^[0-9\-\$\:\.\+\/]+$/)&&(e="A"+e+"A");var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e.toUpperCase(),r));return n.text=n.options.text||n.text.replace(/[A-D]/g,""),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,ji.default),Mi(t,[{key:"valid",value:function(){return-1!==this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/)}},{key:"encode",value:function(){for(var t=[],e=this.getEncodings(),r=0;r<this.data.length;r++)t.push(e[this.data.charAt(r)]),r!==this.data.length-1&&t.push("0");return{text:this.text,data:t.join("")}}},{key:"getEncodings",value:function(){return{0:"101010011",1:"101011001",2:"101001011",3:"110010101",4:"101101001",5:"110101001",6:"100101011",7:"100101101",8:"100110101",9:"110100101","-":"101001101",$:"101100101",":":"1101011011","/":"1101101011",".":"1101101101","+":"1011011011",A:"1011001001",B:"1001001011",C:"1010010011",D:"1010011001"}}}]),t}();ki.codabar=Ri;var Ti={},Ii={},Li={};Object.defineProperty(Li,"__esModule",{value:!0}),Li.SYMBOLS=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","($)","(%)","(/)","(+)","ÿ"],Li.BINARIES=["100010100","101001000","101000100","101000010","100101000","100100100","100100010","101010000","100010010","100001010","110101000","110100100","110100010","110010100","110010010","110001010","101101000","101100100","101100010","100110100","100011010","101011000","101001100","101000110","100101100","100010110","110110100","110110010","110101100","110100110","110010110","110011010","101101100","101100110","100110110","100111010","100101110","111010100","111010010","111001010","101101110","101110110","110101110","100100110","111011010","111010110","100110010","101011110"],Li.MULTI_SYMBOLS={"\0":["(%)","U"],"":["($)","A"],"":["($)","B"],"":["($)","C"],"":["($)","D"],"":["($)","E"],"":["($)","F"],"":["($)","G"],"\b":["($)","H"],"\t":["($)","I"],"\n":["($)","J"],"\v":["($)","K"],"\f":["($)","L"],"\r":["($)","M"],"":["($)","N"],"":["($)","O"],"":["($)","P"],"":["($)","Q"],"":["($)","R"],"":["($)","S"],"":["($)","T"],"":["($)","U"],"":["($)","V"],"":["($)","W"],"":["($)","X"],"":["($)","Y"],"":["($)","Z"],"":["(%)","A"],"":["(%)","B"],"":["(%)","C"],"":["(%)","D"],"":["(%)","E"],"!":["(/)","A"],'"':["(/)","B"],"#":["(/)","C"],"&":["(/)","F"],"'":["(/)","G"],"(":["(/)","H"],")":["(/)","I"],"*":["(/)","J"],",":["(/)","L"],":":["(/)","Z"],";":["(%)","F"],"<":["(%)","G"],"=":["(%)","H"],">":["(%)","I"],"?":["(%)","J"],"@":["(%)","V"],"[":["(%)","K"],"\\":["(%)","L"],"]":["(%)","M"],"^":["(%)","N"],_:["(%)","O"],"`":["(%)","W"],a:["(+)","A"],b:["(+)","B"],c:["(+)","C"],d:["(+)","D"],e:["(+)","E"],f:["(+)","F"],g:["(+)","G"],h:["(+)","H"],i:["(+)","I"],j:["(+)","J"],k:["(+)","K"],l:["(+)","L"],m:["(+)","M"],n:["(+)","N"],o:["(+)","O"],p:["(+)","P"],q:["(+)","Q"],r:["(+)","R"],s:["(+)","S"],t:["(+)","T"],u:["(+)","U"],v:["(+)","V"],w:["(+)","W"],x:["(+)","X"],y:["(+)","Y"],z:["(+)","Z"],"{":["(%)","P"],"|":["(%)","Q"],"}":["(%)","R"],"~":["(%)","S"],"":["(%)","T"]},Object.defineProperty(Ii,"__esModule",{value:!0});var Hi=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),zi=Li,Di=function(t){return t&&t.__esModule?t:{default:t}}(Er);var Ni=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Di.default),Hi(t,[{key:"valid",value:function(){return/^[0-9A-Z\-. $/+%]+$/.test(this.data)}},{key:"encode",value:function(){var e=this.data.split("").flatMap((function(t){return zi.MULTI_SYMBOLS[t]||t})),r=e.map((function(e){return t.getEncoding(e)})).join(""),n=t.checksum(e,20),o=t.checksum(e.concat(n),15);return{text:this.text,data:t.getEncoding("ÿ")+r+t.getEncoding(n)+t.getEncoding(o)+t.getEncoding("ÿ")+"1"}}}],[{key:"getEncoding",value:function(e){return zi.BINARIES[t.symbolValue(e)]}},{key:"getSymbol",value:function(t){return zi.SYMBOLS[t]}},{key:"symbolValue",value:function(t){return zi.SYMBOLS.indexOf(t)}},{key:"checksum",value:function(e,r){var n=e.slice().reverse().reduce((function(e,n,o){var i=o%r+1;return e+t.symbolValue(n)*i}),0);return t.getSymbol(n%47)}}]),t}();Ii.default=Ni;var Ui={};Object.defineProperty(Ui,"__esModule",{value:!0});var Fi=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Gi=function(t){return t&&t.__esModule?t:{default:t}}(Ii);var Xi=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Gi.default),Fi(t,[{key:"valid",value:function(){return/^[\x00-\x7f]+$/.test(this.data)}}]),t}();Ui.default=Xi,Object.defineProperty(Ti,"__esModule",{value:!0}),Ti.CODE93FullASCII=Ti.CODE93=void 0;var $i=Ki(Ii),Vi=Ki(Ui);function Ki(t){return t&&t.__esModule?t:{default:t}}Ti.CODE93=$i.default,Ti.CODE93FullASCII=Vi.default;var Wi={};Object.defineProperty(Wi,"__esModule",{value:!0}),Wi.GenericBarcode=void 0;var Yi=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Ji=function(t){return t&&t.__esModule?t:{default:t}}(Er);var Qi=function(){function t(e,r){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(t,Ji.default),Yi(t,[{key:"encode",value:function(){return{data:"10101010101010101010101010101010101010101",text:this.text}}},{key:"valid",value:function(){return!0}}]),t}();Wi.GenericBarcode=Qi,Object.defineProperty(wr,"__esModule",{value:!0});var qi=mr,Zi=jr,ta=Sn,ea=Lo,ra=Qo,na=Si,oa=ki,ia=Ti,aa=Wi;wr.default={CODE39:qi.CODE39,CODE128:Zi.CODE128,CODE128A:Zi.CODE128A,CODE128B:Zi.CODE128B,CODE128C:Zi.CODE128C,EAN13:ta.EAN13,EAN8:ta.EAN8,EAN5:ta.EAN5,EAN2:ta.EAN2,UPC:ta.UPC,UPCE:ta.UPCE,ITF14:ea.ITF14,ITF:ea.ITF,MSI:ra.MSI,MSI10:ra.MSI10,MSI11:ra.MSI11,MSI1010:ra.MSI1010,MSI1110:ra.MSI1110,pharmacode:na.pharmacode,codabar:oa.codabar,CODE93:ia.CODE93,CODE93FullASCII:ia.CODE93FullASCII,GenericBarcode:aa.GenericBarcode};var ua={};Object.defineProperty(ua,"__esModule",{value:!0});var sa=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t};ua.default=function(t,e){return sa({},t,e)};var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=function(t){var e=[];return function t(r){if(Array.isArray(r))for(var n=0;n<r.length;n++)t(r[n]);else r.text=r.text||"",r.data=r.data||"",e.push(r)}(t),e};var fa={};Object.defineProperty(fa,"__esModule",{value:!0}),fa.default=function(t){return t.marginTop=t.marginTop||t.margin,t.marginBottom=t.marginBottom||t.margin,t.marginRight=t.marginRight||t.margin,t.marginLeft=t.marginLeft||t.margin,t};var la={},ha={},da={};Object.defineProperty(da,"__esModule",{value:!0}),da.default=function(t){var e=["width","height","textMargin","fontSize","margin","marginTop","marginBottom","marginLeft","marginRight"];for(var r in e)e.hasOwnProperty(r)&&"string"==typeof t[r=e[r]]&&(t[r]=parseInt(t[r],10));"string"==typeof t.displayValue&&(t.displayValue="false"!=t.displayValue);return t};var pa={};Object.defineProperty(pa,"__esModule",{value:!0});var va={width:2,height:100,format:"auto",displayValue:!0,fontOptions:"",font:"monospace",text:void 0,textAlign:"center",textPosition:"bottom",textMargin:2,fontSize:20,background:"#ffffff",lineColor:"#000000",margin:10,marginTop:void 0,marginBottom:void 0,marginLeft:void 0,marginRight:void 0,valid:function(){}};pa.default=va,Object.defineProperty(ha,"__esModule",{value:!0});var ga=_a(da),ya=_a(pa);function _a(t){return t&&t.__esModule?t:{default:t}}ha.default=function(t){var e={};for(var r in ya.default)ya.default.hasOwnProperty(r)&&(t.hasAttribute("jsbarcode-"+r.toLowerCase())&&(e[r]=t.getAttribute("jsbarcode-"+r.toLowerCase())),t.hasAttribute("data-"+r.toLowerCase())&&(e[r]=t.getAttribute("data-"+r.toLowerCase())));return e.value=t.getAttribute("jsbarcode-value")||t.getAttribute("data-value"),e=(0,ga.default)(e)};var ba={},wa={},ma={};Object.defineProperty(ma,"__esModule",{value:!0}),ma.getTotalWidthOfEncodings=ma.calculateEncodingAttributes=ma.getBarcodePadding=ma.getEncodingHeight=ma.getMaximumHeightOfEncodings=void 0;var Ea=function(t){return t&&t.__esModule?t:{default:t}}(ua);function xa(t,e){return e.height+(e.displayValue&&t.text.length>0?e.fontSize+e.textMargin:0)+e.marginTop+e.marginBottom}function Oa(t,e,r){if(r.displayValue&&e<t){if("center"==r.textAlign)return Math.floor((t-e)/2);if("left"==r.textAlign)return 0;if("right"==r.textAlign)return Math.floor(t-e)}return 0}function Ca(t,e,r){var n;if(r)n=r;else{if("undefined"==typeof document)return 0;n=document.createElement("canvas").getContext("2d")}n.font=e.fontOptions+" "+e.fontSize+"px "+e.font;var o=n.measureText(t);return o?o.width:0}ma.getMaximumHeightOfEncodings=function(t){for(var e=0,r=0;r<t.length;r++)t[r].height>e&&(e=t[r].height);return e},ma.getEncodingHeight=xa,ma.getBarcodePadding=Oa,ma.calculateEncodingAttributes=function(t,e,r){for(var n=0;n<t.length;n++){var o,i=t[n],a=(0,Ea.default)(e,i.options);o=a.displayValue?Ca(i.text,a,r):0;var u=i.data.length*a.width;i.width=Math.ceil(Math.max(o,u)),i.height=xa(i,a),i.barcodePadding=Oa(o,u,a)}},ma.getTotalWidthOfEncodings=function(t){for(var e=0,r=0;r<t.length;r++)e+=t[r].width;return e},Object.defineProperty(wa,"__esModule",{value:!0});var Sa=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),Aa=function(t){return t&&t.__esModule?t:{default:t}}(ua),Ba=ma;var Pa=function(){function t(e,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.canvas=e,this.encodings=r,this.options=n}return Sa(t,[{key:"render",value:function(){if(!this.canvas.getContext)throw new Error("The browser does not support canvas.");this.prepareCanvas();for(var t=0;t<this.encodings.length;t++){var e=(0,Aa.default)(this.options,this.encodings[t].options);this.drawCanvasBarcode(e,this.encodings[t]),this.drawCanvasText(e,this.encodings[t]),this.moveCanvasDrawing(this.encodings[t])}this.restoreCanvas()}},{key:"prepareCanvas",value:function(){var t=this.canvas.getContext("2d");t.save(),(0,Ba.calculateEncodingAttributes)(this.encodings,this.options,t);var e=(0,Ba.getTotalWidthOfEncodings)(this.encodings),r=(0,Ba.getMaximumHeightOfEncodings)(this.encodings);this.canvas.width=e+this.options.marginLeft+this.options.marginRight,this.canvas.height=r,t.clearRect(0,0,this.canvas.width,this.canvas.height),this.options.background&&(t.fillStyle=this.options.background,t.fillRect(0,0,this.canvas.width,this.canvas.height)),t.translate(this.options.marginLeft,0)}},{key:"drawCanvasBarcode",value:function(t,e){var r,n=this.canvas.getContext("2d"),o=e.data;r="top"==t.textPosition?t.marginTop+t.fontSize+t.textMargin:t.marginTop,n.fillStyle=t.lineColor;for(var i=0;i<o.length;i++){var a=i*t.width+e.barcodePadding;"1"===o[i]?n.fillRect(a,r,t.width,t.height):o[i]&&n.fillRect(a,r,t.width,t.height*o[i])}}},{key:"drawCanvasText",value:function(t,e){var r,n,o=this.canvas.getContext("2d"),i=t.fontOptions+" "+t.fontSize+"px "+t.font;t.displayValue&&(n="top"==t.textPosition?t.marginTop+t.fontSize-t.textMargin:t.height+t.textMargin+t.marginTop+t.fontSize,o.font=i,"left"==t.textAlign||e.barcodePadding>0?(r=0,o.textAlign="left"):"right"==t.textAlign?(r=e.width-1,o.textAlign="right"):(r=e.width/2,o.textAlign="center"),o.fillText(e.text,r,n))}},{key:"moveCanvasDrawing",value:function(t){this.canvas.getContext("2d").translate(t.width,0)}},{key:"restoreCanvas",value:function(){this.canvas.getContext("2d").restore()}}]),t}();wa.default=Pa;var ka={};Object.defineProperty(ka,"__esModule",{value:!0});var Ma=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}(),ja=function(t){return t&&t.__esModule?t:{default:t}}(ua),Ra=ma;var Ta="http://www.w3.org/2000/svg",Ia=function(){function t(e,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.svg=e,this.encodings=r,this.options=n,this.document=n.xmlDocument||document}return Ma(t,[{key:"render",value:function(){var t=this.options.marginLeft;this.prepareSVG();for(var e=0;e<this.encodings.length;e++){var r=this.encodings[e],n=(0,ja.default)(this.options,r.options),o=this.createGroup(t,n.marginTop,this.svg);this.setGroupOptions(o,n),this.drawSvgBarcode(o,n,r),this.drawSVGText(o,n,r),t+=r.width}}},{key:"prepareSVG",value:function(){for(;this.svg.firstChild;)this.svg.removeChild(this.svg.firstChild);(0,Ra.calculateEncodingAttributes)(this.encodings,this.options);var t=(0,Ra.getTotalWidthOfEncodings)(this.encodings),e=(0,Ra.getMaximumHeightOfEncodings)(this.encodings),r=t+this.options.marginLeft+this.options.marginRight;this.setSvgAttributes(r,e),this.options.background&&this.drawRect(0,0,r,e,this.svg).setAttribute("style","fill:"+this.options.background+";")}},{key:"drawSvgBarcode",value:function(t,e,r){var n,o=r.data;n="top"==e.textPosition?e.fontSize+e.textMargin:0;for(var i=0,a=0,u=0;u<o.length;u++)a=u*e.width+r.barcodePadding,"1"===o[u]?i++:i>0&&(this.drawRect(a-e.width*i,n,e.width*i,e.height,t),i=0);i>0&&this.drawRect(a-e.width*(i-1),n,e.width*i,e.height,t)}},{key:"drawSVGText",value:function(t,e,r){var n,o,i=this.document.createElementNS(Ta,"text");e.displayValue&&(i.setAttribute("style","font:"+e.fontOptions+" "+e.fontSize+"px "+e.font),o="top"==e.textPosition?e.fontSize-e.textMargin:e.height+e.textMargin+e.fontSize,"left"==e.textAlign||r.barcodePadding>0?(n=0,i.setAttribute("text-anchor","start")):"right"==e.textAlign?(n=r.width-1,i.setAttribute("text-anchor","end")):(n=r.width/2,i.setAttribute("text-anchor","middle")),i.setAttribute("x",n),i.setAttribute("y",o),i.appendChild(this.document.createTextNode(r.text)),t.appendChild(i))}},{key:"setSvgAttributes",value:function(t,e){var r=this.svg;r.setAttribute("width",t+"px"),r.setAttribute("height",e+"px"),r.setAttribute("x","0px"),r.setAttribute("y","0px"),r.setAttribute("viewBox","0 0 "+t+" "+e),r.setAttribute("xmlns",Ta),r.setAttribute("version","1.1"),r.setAttribute("style","transform: translate(0,0)")}},{key:"createGroup",value:function(t,e,r){var n=this.document.createElementNS(Ta,"g");return n.setAttribute("transform","translate("+t+", "+e+")"),r.appendChild(n),n}},{key:"setGroupOptions",value:function(t,e){t.setAttribute("style","fill:"+e.lineColor+";")}},{key:"drawRect",value:function(t,e,r,n,o){var i=this.document.createElementNS(Ta,"rect");return i.setAttribute("x",t),i.setAttribute("y",e),i.setAttribute("width",r),i.setAttribute("height",n),o.appendChild(i),i}}]),t}();ka.default=Ia;var La={};Object.defineProperty(La,"__esModule",{value:!0});var Ha=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}();var za=function(){function t(e,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.object=e,this.encodings=r,this.options=n}return Ha(t,[{key:"render",value:function(){this.object.encodings=this.encodings}}]),t}();La.default=za,Object.defineProperty(ba,"__esModule",{value:!0});var Da=Fa(wa),Na=Fa(ka),Ua=Fa(La);function Fa(t){return t&&t.__esModule?t:{default:t}}ba.default={CanvasRenderer:Da.default,SVGRenderer:Na.default,ObjectRenderer:Ua.default};var Ga={};function Xa(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function $a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function Va(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}Object.defineProperty(Ga,"__esModule",{value:!0});var Ka=function(){function t(e,r){Xa(this,t);var n=$a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.name="InvalidInputException",n.symbology=e,n.input=r,n.message='"'+n.input+'" is not a valid input for '+n.symbology,n}return Va(t,Error),t}(),Wa=function(){function t(){Xa(this,t);var e=$a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="InvalidElementException",e.message="Not supported type to render on",e}return Va(t,Error),t}(),Ya=function(){function t(){Xa(this,t);var e=$a(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.name="NoElementException",e.message="No element to render on.",e}return Va(t,Error),t}();Ga.InvalidInputException=Ka,Ga.InvalidElementException=Wa,Ga.NoElementException=Ya,Object.defineProperty(la,"__esModule",{value:!0});var Ja="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qa=tu(ha),qa=tu(ba),Za=Ga;function tu(t){return t&&t.__esModule?t:{default:t}}function eu(t){if("string"==typeof t)return function(t){var e=document.querySelectorAll(t);if(0===e.length)return;for(var r=[],n=0;n<e.length;n++)r.push(eu(e[n]));return r}(t);if(Array.isArray(t)){for(var e=[],r=0;r<t.length;r++)e.push(eu(t[r]));return e}if("undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLImageElement)return function(t){var e=document.createElement("canvas");return{element:e,options:(0,Qa.default)(t),renderer:qa.default.CanvasRenderer,afterRender:function(){t.setAttribute("src",e.toDataURL())}}}(t);if(t&&t.nodeName&&"svg"===t.nodeName.toLowerCase()||"undefined"!=typeof SVGElement&&t instanceof SVGElement)return{element:t,options:(0,Qa.default)(t),renderer:qa.default.SVGRenderer};if("undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement)return{element:t,options:(0,Qa.default)(t),renderer:qa.default.CanvasRenderer};if(t&&t.getContext)return{element:t,renderer:qa.default.CanvasRenderer};if(t&&"object"===(void 0===t?"undefined":Ja(t))&&!t.nodeName)return{element:t,renderer:qa.default.ObjectRenderer};throw new Za.InvalidElementException}la.default=eu;var ru={};Object.defineProperty(ru,"__esModule",{value:!0});var nu=function(){function t(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),e}}();var ou=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.api=e}return nu(t,[{key:"handleCatch",value:function(t){if("InvalidInputException"!==t.name)throw t;if(this.api._options.valid===this.api._defaults.valid)throw t.message;this.api._options.valid(!1),this.api.render=function(){}}},{key:"wrapBarcodeCall",value:function(t){try{var e=t.apply(void 0,arguments);return this.api._options.valid(!0),e}catch(r){return this.handleCatch(r),this.api}}}]),t}();ru.default=ou;var iu=pu(wr),au=pu(ua),uu=pu(ca),su=pu(fa),cu=pu(la),fu=pu(da),lu=pu(ru),hu=Ga,du=pu(pa);function pu(t){return t&&t.__esModule?t:{default:t}}var vu=function(){},gu=function(t,e,r){var n=new vu;if(void 0===t)throw Error("No element to render on was provided.");return n._renderProperties=(0,cu.default)(t),n._encodings=[],n._options=du.default,n._errorHandler=new lu.default(n),void 0!==e&&((r=r||{}).format||(r.format=wu()),n.options(r)[r.format](e,r).render()),n};for(var yu in gu.getModule=function(t){return iu.default[t]},iu.default)iu.default.hasOwnProperty(yu)&&_u(iu.default,yu);function _u(t,e){vu.prototype[e]=vu.prototype[e.toUpperCase()]=vu.prototype[e.toLowerCase()]=function(r,n){var o=this;return o._errorHandler.wrapBarcodeCall((function(){n.text=void 0===n.text?void 0:""+n.text;var i=(0,au.default)(o._options,n);i=(0,fu.default)(i);var a=t[e],u=bu(r,a,i);return o._encodings.push(u),o}))}}function bu(t,e,r){var n=new e(t=""+t,r);if(!n.valid())throw new hu.InvalidInputException(n.constructor.name,t);var o=n.encode();o=(0,uu.default)(o);for(var i=0;i<o.length;i++)o[i].options=(0,au.default)(r,o[i].options);return o}function wu(){return iu.default.CODE128?"CODE128":Object.keys(iu.default)[0]}function mu(t,e,r){e=(0,uu.default)(e);for(var n=0;n<e.length;n++)e[n].options=(0,au.default)(r,e[n].options),(0,su.default)(e[n].options);(0,su.default)(r),new(0,t.renderer)(t.element,e,r).render(),t.afterRender&&t.afterRender()}vu.prototype.options=function(t){return this._options=(0,au.default)(this._options,t),this},vu.prototype.blank=function(t){var e=new Array(t+1).join("0");return this._encodings.push({data:e}),this},vu.prototype.init=function(){var t;if(this._renderProperties)for(var e in Array.isArray(this._renderProperties)||(this._renderProperties=[this._renderProperties]),this._renderProperties){t=this._renderProperties[e];var r=(0,au.default)(this._options,t.options);"auto"==r.format&&(r.format=wu()),this._errorHandler.wrapBarcodeCall((function(){var e=bu(r.value,iu.default[r.format.toUpperCase()],r);mu(t,e,r)}))}},vu.prototype.render=function(){if(!this._renderProperties)throw new hu.NoElementException;if(Array.isArray(this._renderProperties))for(var t=0;t<this._renderProperties.length;t++)mu(this._renderProperties[t],this._encodings,this._options);else mu(this._renderProperties,this._encodings,this._options);return this},vu.prototype._defaults=du.default,"undefined"!=typeof window&&(window.JsBarcode=gu),"undefined"!=typeof jQuery&&(jQuery.fn.JsBarcode=function(t,e){var r=[];return jQuery(this).each((function(){r.push(this)})),gu(r,t,e)});const Eu=r(gu);export{Xt as C,Eu as J,$t as b,i as s};
