# 🔧 **GESTION DES RÉPARATIONS - DOCUMENTATION COMPLÈTE**

## 📋 **APERÇU GÉNÉRAL**

Le système de gestion des réparations iRepair_dz est une application Electron complète pour la gestion d'un atelier de réparation de téléphones mobiles et appareils électroniques. Il offre une solution intégrée pour le suivi des réparations, la gestion des clients, l'impression thermique, et la facturation.

---

## 🎯 **FONCTIONNALITÉS PRINCIPALES**

### **1. GESTION DES RÉPARATIONS**
- **Nouveau Bon Pour Réparation**: Création de nouveaux ordres de réparation
- **Suivi des statuts**: En Cours → Terminé → Récupération Client
- **Codes QR et Codes-barres**: Génération automatique pour le suivi
- **Impression thermique**: Tickets de réparation 80x80mm et tickets de collage 40x60mm

### **2. SYSTÈME DE PRIX**
- **Prix de Réparation**: Prix final facturé au client (défini dans "Nouveau Bon Pour Prix de Réparation")
- **Prix des Pièces**: Prix d'achat des pièces (pour suivi fournisseur uniquement)
- **Calcul d'Intérêt**: Différence entre prix de réparation et prix des pièces
- **Synchronisation TVA**: Taux de TVA synchronisé depuis les paramètres du magasin

### **3. IMPRESSION THERMIQUE AVANCÉE**
- **Tickets de Réparation**: Format 80x80mm avec QR code, informations client, et prix
- **Tickets de Collage**: Format 40x60mm pour identification rapide
- **Factures Finales**: Impression avec TVA synchronisée depuis les paramètres
- **Support Multi-langues**: Arabe (RTL), Français, Anglais

### **4. GESTION DES PROBLÈMES**
- **Problèmes Prédéfinis**: LCD, Batterie, Port de charge, etc.
- **Problèmes Personnalisés**: Système CRUD complet avec modal moderne
- **Synchronisation localStorage**: Persistance des problèmes personnalisés
- **Interface Moderne**: Modal avec ajout/modification/suppression

---

## 💾 **STRUCTURE DES DONNÉES**

### **Réparation (Repair Object)**
```javascript
{
  id: "REP-1704067200000",
  repairBarcode: "000000001",
  clientName: "Ahmed Mohamed",
  clientPhone: "+213555123456",
  deviceName: "iPhone 14 Pro",
  problemType: "lcd",
  problemDescription: "Écran LCD cassé",
  remarks: "Client souhaite récupérer avant 17h - Tel: +213666789012",
  depositDate: "2024-01-01",
  status: "enCours", // enCours | termine | recuperationClient
  repairPrice: 2800, // Prix final facturé au client
  partsPrice: 1200,  // Prix d'achat des pièces (fournisseur)
  supplierName: "TechParts DZ",
  verificationPrice: 0
}
```

### **Problème Personnalisé (Custom Problem)**
```javascript
{
  id: "PROB-1704067200000",
  name: "Écran OLED",
  description: "Remplacement écran OLED avec calibrage",
  createdAt: "2024-01-01T10:00:00Z"
}
```

---

## 🔄 **WORKFLOW DE RÉPARATION**

### **1. CRÉATION D'UNE RÉPARATION**
1. **Nouveau Bon Pour Réparation**: Saisie des informations client et appareil
2. **Sélection du Problème**: Choix parmi problèmes prédéfinis ou personnalisés
3. **Génération Automatique**: Code-barres et QR code créés automatiquement
4. **Impression Ticket**: Ticket de réparation 80x80mm avec toutes les informations

### **2. TRAITEMENT DE LA RÉPARATION**
1. **Statut "En Cours"**: Réparation en cours de traitement
2. **Ajout Prix des Pièces**: Saisie du coût des pièces pour suivi fournisseur
3. **Nouveau Bon Pour Prix de Réparation**: Définition du prix final client
4. **Passage à "Terminé"**: Réparation prête pour récupération

### **3. RÉCUPÉRATION CLIENT**
1. **Scan QR Code**: Identification rapide de la réparation
2. **Vérification Informations**: Contrôle client et appareil
3. **Impression Facture Finale**: Facture avec TVA synchronisée
4. **Statut "Récupération Client"**: Réparation terminée et facturée

---

## 🖨️ **SYSTÈME D'IMPRESSION THERMIQUE**

### **Types d'Impression**
1. **Ticket de Réparation (80x80mm)**:
   - Logo du magasin
   - Informations client (nom, téléphone)
   - Détails appareil et problème
   - Remarques (incluant numéro de téléphone)
   - Code-barres et QR code
   - Prix de réparation

2. **Ticket de Collage (40x60mm)**:
   - Format compact pour collage sur appareil
   - Nom client et prix
   - Code-barres pour identification
   - Remarques avec téléphone

3. **Facture Finale**:
   - Informations complètes de facturation
   - Prix de réparation + TVA synchronisée
   - Calcul automatique du total

### **Fonctionnalités d'Impression**
- **Impression Directe**: Envoi direct vers imprimante thermique
- **Aperçu Web**: Fenêtre de prévisualisation avant impression
- **Format Responsive**: Adaptation automatique au format thermique
- **Support RTL**: Mise en page adaptée pour l'arabe

---

## 🎨 **INTERFACE UTILISATEUR**

### **Design Moderne**
- **Interface Responsive**: Adaptation mobile et desktop
- **Thème Sombre/Clair**: Basculement automatique
- **Multi-langues**: Arabe (RTL), Français, Anglais
- **Animations Fluides**: Transitions CSS modernes

### **Composants Principaux**
1. **Tableau des Réparations**: Vue d'ensemble avec filtres et recherche
2. **Modals Modernes**: Interfaces popup pour toutes les actions
3. **Scanner QR**: Intégration caméra pour scan des codes
4. **Gestionnaire de Problèmes**: CRUD complet pour problèmes personnalisés

---

## ⚙️ **CONFIGURATION ET PARAMÈTRES**

### **Paramètres du Magasin**
```javascript
{
  storeName: "iCalDZ Store",
  storePhone: "+*********** 456",
  storeAddress: "الجزائر العاصمة، الجزائر",
  storeLogo: "data:image/...",
  taxRate: 0, // Taux de TVA (0% par défaut)
  currency: "DZD"
}
```

### **Synchronisation TVA**
- **Paramètres Centralisés**: Taux de TVA défini dans les paramètres
- **Application Automatique**: Utilisation dans toutes les factures
- **Défaut à 0%**: Pas de TVA par défaut (modifiable)

---

## 🔧 **ASPECTS TECHNIQUES**

### **Technologies Utilisées**
- **Frontend**: React 18 + JSX
- **Desktop**: Electron
- **Stockage**: localStorage avec préfixes (`icaldz-`)
- **Impression**: HTML/CSS vers imprimante thermique
- **QR Codes**: Bibliothèque qrcode.js
- **Codes-barres**: JsBarcode pour génération

### **Gestion Mémoire**
- **Optimisation Electron**: Nettoyage automatique toutes les 5 minutes
- **Prévention Blocage**: Évite le blocage de 20 minutes
- **Gestion États**: React hooks pour état local
- **Persistance**: Sauvegarde automatique localStorage

### **Sécurité**
- **Validation Données**: Contrôles côté client
- **Sanitisation**: Nettoyage des entrées utilisateur
- **Gestion Erreurs**: Try-catch complets avec notifications

---

## 📊 **CALCULS ET LOGIQUE MÉTIER**

### **Calcul des Prix**
```javascript
// Prix final client (défini manuellement)
const repairPrice = 2800;

// Prix des pièces (pour suivi fournisseur)
const partsPrice = 1200;

// Calcul d'intérêt (marge bénéficiaire)
const interestRate = repairPrice - partsPrice; // 1600

// Calcul TVA (synchronisée depuis paramètres)
const taxRate = storeSettings.taxRate || 0; // 0% par défaut
const taxAmount = (repairPrice * taxRate) / 100;
const finalTotal = repairPrice + taxAmount;
```

### **Formatage Prix**
```javascript
// Format propre sans virgules ni décimales
const formatPrice = (price) => {
  const amount = Math.round(price || 0).toString();
  return currentLanguage === 'ar' ? `${amount} د.ج` : `${amount} DZD`;
};
```

---

## 🚀 **DÉPLOIEMENT ET UTILISATION**

### **Installation**
1. **Développement**: `npm run dev`
2. **Production**: `npm run build`
3. **Electron**: `npm run electron`

### **Utilisation Quotidienne**
1. **Réception Client**: Création nouveau bon de réparation
2. **Traitement**: Suivi des réparations en cours
3. **Récupération**: Scan QR et impression facture finale
4. **Gestion**: Suivi des fournisseurs et pièces

### **Maintenance**
- **Sauvegarde**: Export JSON des données
- **Nettoyage**: Suppression réparations anciennes
- **Mise à jour**: Synchronisation paramètres magasin

---

## 📝 **NOTES IMPORTANTES**

1. **Prix de Réparation**: Toujours défini dans "Nouveau Bon Pour Prix de Réparation"
2. **Prix des Pièces**: Uniquement pour suivi fournisseur, pas facturé au client
3. **TVA**: Synchronisée depuis paramètres (0% par défaut)
4. **Remarques**: Peuvent contenir numéro de téléphone affiché sur tickets
5. **QR Codes**: Format propre "2800 DZD" au lieu de "2,800.00 DZD"
6. **Electron**: Redémarrage nécessaire après 20 minutes d'utilisation intensive

---

*Documentation générée pour iRepair_dz - Système de Gestion des Réparations*
*Version: 2024 - Développé avec React + Electron*
