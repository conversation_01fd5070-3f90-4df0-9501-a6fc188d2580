/**
 * 🧠 MEMORY MANAGER - COMPREHENSIVE MEMORY LEAK PREVENTION SYSTEM
 * 
 * Features:
 * - Automatic memory cleanup and garbage collection
 * - Timeout and interval management
 * - Event listener cleanup
 * - DOM element cleanup
 * - Scanner health monitoring
 * - System recovery mechanisms
 * - Performance monitoring
 */

export class MemoryManager {
  constructor() {
    this.isInitialized = false;
    this.startTime = Date.now();
    this.cleanupIntervals = new Set();
    this.activeTimeouts = new Set();
    this.activeIntervals = new Set();
    this.eventListeners = new Map();
    this.domObservers = new Set();
    this.memoryStats = {
      totalCleanups: 0,
      timeoutsCleared: 0,
      intervalsCleared: 0,
      listenersRemoved: 0,
      lastCleanup: Date.now(),
      memoryUsage: 0
    };
    
    // System health monitoring
    this.systemHealth = {
      isHealthy: true,
      lastActivity: Date.now(),
      errorCount: 0,
      warningCount: 0,
      criticalErrors: []
    };
    
    this.init();
  }

  /**
   * Initialize memory management system
   */
  init() {
    try {
      // Set up periodic cleanup
      this.setupPeriodicCleanup();
      
      // Monitor memory usage
      this.setupMemoryMonitoring();
      
      // Set up emergency recovery
      this.setupEmergencyRecovery();
      
      // Monitor system health
      this.setupHealthMonitoring();
      
      this.isInitialized = true;
      console.log('🧠 Memory Manager: System initialized successfully');
    } catch (error) {
      console.error('🧠 Memory Manager: Initialization failed', error);
      this.systemHealth.criticalErrors.push({
        error: error.message,
        timestamp: Date.now(),
        context: 'initialization'
      });
    }
  }

  /**
   * Set up periodic cleanup system - FIXED: Reduced frequency to prevent blocking
   */
  setupPeriodicCleanup() {
    // FIXED: Main cleanup every 15 minutes (was 5 minutes)
    const mainCleanupInterval = setInterval(() => {
      this.performMainCleanup();
    }, 15 * 60 * 1000);

    // FIXED: Deep cleanup every 2 hours (was 30 minutes)
    const deepCleanupInterval = setInterval(() => {
      this.performDeepCleanup();
    }, 2 * 60 * 60 * 1000);

    // FIXED: Emergency cleanup every 4 hours (was 1 hour) - only when really needed
    const emergencyCleanupInterval = setInterval(() => {
      // Only run emergency cleanup if system is actually unhealthy
      if (!this.systemHealth.isHealthy || this.systemHealth.errorCount > 5) {
        this.performEmergencyCleanup();
      }
    }, 4 * 60 * 60 * 1000);

    this.cleanupIntervals.add(mainCleanupInterval);
    this.cleanupIntervals.add(deepCleanupInterval);
    this.cleanupIntervals.add(emergencyCleanupInterval);
  }

  /**
   * Set up memory monitoring - FIXED: Reduced frequency
   */
  setupMemoryMonitoring() {
    if (performance && performance.memory) {
      const memoryMonitorInterval = setInterval(() => {
        this.checkMemoryUsage();
      }, 10 * 60 * 1000); // FIXED: Every 10 minutes (was 2 minutes)

      this.cleanupIntervals.add(memoryMonitorInterval);
    }
  }

  /**
   * Set up emergency recovery system - FIXED: Reduced frequency
   */
  setupEmergencyRecovery() {
    // Monitor for system blocking
    const blockingCheckInterval = setInterval(() => {
      this.checkForSystemBlocking();
    }, 30 * 60 * 1000); // FIXED: Every 30 minutes (was 10 minutes)

    this.cleanupIntervals.add(blockingCheckInterval);
  }

  /**
   * Set up health monitoring - FIXED: Reduced frequency
   */
  setupHealthMonitoring() {
    const healthCheckInterval = setInterval(() => {
      this.updateSystemHealth();
    }, 15 * 60 * 1000); // FIXED: Every 15 minutes (was 5 minutes)

    this.cleanupIntervals.add(healthCheckInterval);
  }

  /**
   * Register a timeout for tracking
   */
  registerTimeout(timeoutId, context = 'unknown') {
    this.activeTimeouts.add({
      id: timeoutId,
      context: context,
      created: Date.now()
    });
    return timeoutId;
  }

  /**
   * Register an interval for tracking
   */
  registerInterval(intervalId, context = 'unknown') {
    this.activeIntervals.add({
      id: intervalId,
      context: context,
      created: Date.now()
    });
    return intervalId;
  }

  /**
   * Register an event listener for tracking
   */
  registerEventListener(element, event, handler, context = 'unknown') {
    const key = `${element.constructor.name}-${event}-${context}`;
    this.eventListeners.set(key, {
      element: element,
      event: event,
      handler: handler,
      context: context,
      created: Date.now()
    });
  }

  /**
   * Clear a specific timeout
   */
  clearTimeout(timeoutId) {
    clearTimeout(timeoutId);
    this.activeTimeouts.forEach(timeout => {
      if (timeout.id === timeoutId) {
        this.activeTimeouts.delete(timeout);
        this.memoryStats.timeoutsCleared++;
      }
    });
  }

  /**
   * Clear a specific interval
   */
  clearInterval(intervalId) {
    clearInterval(intervalId);
    this.activeIntervals.forEach(interval => {
      if (interval.id === intervalId) {
        this.activeIntervals.delete(interval);
        this.memoryStats.intervalsCleared++;
      }
    });
  }

  /**
   * Perform main cleanup
   */
  performMainCleanup() {
    console.log('🧠 Memory Manager: Performing main cleanup...');
    
    try {
      // Clear old timeouts (older than 1 hour)
      this.clearOldTimeouts(60 * 60 * 1000);
      
      // Clear old intervals (older than 2 hours)
      this.clearOldIntervals(2 * 60 * 60 * 1000);
      
      // Clean up DOM elements
      this.cleanupDOMElements();
      
      // Clear scanner timeouts
      this.clearScannerTimeouts();
      
      this.memoryStats.totalCleanups++;
      this.memoryStats.lastCleanup = Date.now();
      
      console.log('🧠 Memory Manager: Main cleanup completed');
    } catch (error) {
      console.error('🧠 Memory Manager: Main cleanup failed', error);
      this.systemHealth.errorCount++;
    }
  }

  /**
   * Perform deep cleanup
   */
  performDeepCleanup() {
    console.log('🧠 Memory Manager: Performing deep cleanup...');
    
    try {
      // Clear all timeouts
      this.clearAllTimeouts();
      
      // Remove old event listeners
      this.removeOldEventListeners();
      
      // Force garbage collection if available
      this.forceGarbageCollection();
      
      // Reset scanner system
      this.resetScannerSystem();
      
      console.log('🧠 Memory Manager: Deep cleanup completed');
    } catch (error) {
      console.error('🧠 Memory Manager: Deep cleanup failed', error);
      this.systemHealth.errorCount++;
    }
  }

  /**
   * Perform emergency cleanup - FIXED: Less aggressive to prevent blocking
   */
  performEmergencyCleanup() {
    console.log('🧠 Memory Manager: Performing emergency cleanup...');

    try {
      // FIXED: Only clear old timeouts/intervals, not ALL of them
      this.clearOldTimeouts(30 * 60 * 1000); // Clear timeouts older than 30 minutes
      this.clearOldIntervals(60 * 60 * 1000); // Clear intervals older than 1 hour

      // Clear scanner timeouts (safe to clear these)
      this.clearScannerTimeouts();

      // FIXED: Don't remove ALL event listeners, only old ones
      this.removeOldEventListeners();

      // FIXED: Only reset scanner system, not all systems
      this.resetScannerSystem();

      // FIXED: Single garbage collection, not multiple
      this.forceGarbageCollection();

      console.log('🧠 Memory Manager: Emergency cleanup completed');
    } catch (error) {
      console.error('🧠 Memory Manager: Emergency cleanup failed', error);
      this.systemHealth.criticalErrors.push({
        error: error.message,
        timestamp: Date.now(),
        context: 'emergency_cleanup'
      });
    }
  }

  /**
   * Clear old timeouts
   */
  clearOldTimeouts(maxAge) {
    const now = Date.now();
    this.activeTimeouts.forEach(timeout => {
      if (now - timeout.created > maxAge) {
        clearTimeout(timeout.id);
        this.activeTimeouts.delete(timeout);
        this.memoryStats.timeoutsCleared++;
      }
    });
  }

  /**
   * Clear old intervals
   */
  clearOldIntervals(maxAge) {
    const now = Date.now();
    this.activeIntervals.forEach(interval => {
      if (now - interval.created > maxAge) {
        clearInterval(interval.id);
        this.activeIntervals.delete(interval);
        this.memoryStats.intervalsCleared++;
      }
    });
  }

  /**
   * Clear all timeouts
   */
  clearAllTimeouts() {
    this.activeTimeouts.forEach(timeout => {
      clearTimeout(timeout.id);
      this.memoryStats.timeoutsCleared++;
    });
    this.activeTimeouts.clear();
  }

  /**
   * Clear all intervals
   */
  clearAllIntervals() {
    this.activeIntervals.forEach(interval => {
      clearInterval(interval.id);
      this.memoryStats.intervalsCleared++;
    });
    this.activeIntervals.clear();
  }

  /**
   * Remove old event listeners
   */
  removeOldEventListeners() {
    const now = Date.now();
    const maxAge = 2 * 60 * 60 * 1000; // 2 hours
    
    this.eventListeners.forEach((listener, key) => {
      if (now - listener.created > maxAge) {
        try {
          listener.element.removeEventListener(listener.event, listener.handler);
          this.eventListeners.delete(key);
          this.memoryStats.listenersRemoved++;
        } catch (error) {
          console.warn('🧠 Memory Manager: Failed to remove event listener', error);
        }
      }
    });
  }

  /**
   * Remove all event listeners
   */
  removeAllEventListeners() {
    this.eventListeners.forEach((listener, key) => {
      try {
        listener.element.removeEventListener(listener.event, listener.handler);
        this.memoryStats.listenersRemoved++;
      } catch (error) {
        console.warn('🧠 Memory Manager: Failed to remove event listener', error);
      }
    });
    this.eventListeners.clear();
  }

  /**
   * Clean up DOM elements
   */
  cleanupDOMElements() {
    // Remove orphaned elements
    const orphanedElements = document.querySelectorAll('[data-cleanup="true"]');
    orphanedElements.forEach(element => {
      try {
        element.remove();
      } catch (error) {
        console.warn('🧠 Memory Manager: Failed to remove DOM element', error);
      }
    });
    
    // Clean up print iframes
    const printIframes = document.querySelectorAll('iframe[style*="position: absolute"]');
    printIframes.forEach(iframe => {
      try {
        if (iframe.style.left === '-9999px') {
          iframe.remove();
        }
      } catch (error) {
        console.warn('🧠 Memory Manager: Failed to remove print iframe', error);
      }
    });
  }

  /**
   * Clear scanner timeouts
   */
  clearScannerTimeouts() {
    const scannerTimeouts = [
      'dashboardScannerTimeout',
      'salesScannerTimeout',
      'salesScannerValidationTimeout',
      'editScannerValidationTimeout',
      'productScannerTimeout'
    ];
    
    scannerTimeouts.forEach(timeoutName => {
      if (window[timeoutName]) {
        clearTimeout(window[timeoutName]);
        window[timeoutName] = null;
      }
    });
  }

  /**
   * Reset scanner system
   */
  resetScannerSystem() {
    // Reset barcode shortcut manager
    if (window.barcodeShortcutManager) {
      window.barcodeShortcutManager.isBarcodeActive = false;
      window.barcodeShortcutManager.setShortcutsEnabled(true);
    }
    
    // Clear scanner timeouts
    this.clearScannerTimeouts();
  }

  /**
   * Reset all systems
   */
  resetAllSystems() {
    this.resetScannerSystem();
    
    // Reset keyboard shortcuts
    if (window.KeyboardShortcuts) {
      window.KeyboardShortcuts.setEnabled(true);
    }
    
    // Reset sound manager
    if (window.SoundManager) {
      try {
        window.SoundManager.cleanup();
      } catch (error) {
        console.warn('🧠 Memory Manager: Failed to cleanup sound manager', error);
      }
    }
  }

  /**
   * Force garbage collection
   */
  forceGarbageCollection() {
    if (window.gc && typeof window.gc === 'function') {
      try {
        window.gc();
        console.log('🧠 Memory Manager: Forced garbage collection');
      } catch (error) {
        console.warn('🧠 Memory Manager: Garbage collection not available', error);
      }
    }
  }

  /**
   * Check memory usage
   */
  checkMemoryUsage() {
    if (performance && performance.memory) {
      const memory = performance.memory;
      this.memoryStats.memoryUsage = memory.usedJSHeapSize;
      
      // Check for memory leaks
      const memoryThreshold = 100 * 1024 * 1024; // 100MB
      if (memory.usedJSHeapSize > memoryThreshold) {
        console.warn('🧠 Memory Manager: High memory usage detected', {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB'
        });
        
        // Trigger emergency cleanup
        this.performEmergencyCleanup();
      }
    }
  }

  /**
   * Check for system blocking
   */
  checkForSystemBlocking() {
    const now = Date.now();
    const timeSinceLastActivity = now - this.systemHealth.lastActivity;
    
    // If no activity for more than 30 minutes, consider system potentially blocked
    if (timeSinceLastActivity > 30 * 60 * 1000) {
      console.warn('🧠 Memory Manager: Potential system blocking detected');
      this.performEmergencyCleanup();
      this.systemHealth.warningCount++;
    }
  }

  /**
   * Update system health
   */
  updateSystemHealth() {
    const now = Date.now();
    const uptime = now - this.startTime;
    
    this.systemHealth.lastActivity = now;
    this.systemHealth.isHealthy = this.systemHealth.errorCount < 10;
    
    // Log health status every hour
    if (uptime % (60 * 60 * 1000) < 5 * 60 * 1000) {
      console.log('🧠 Memory Manager: System health status', {
        uptime: Math.round(uptime / 1000 / 60) + ' minutes',
        isHealthy: this.systemHealth.isHealthy,
        errorCount: this.systemHealth.errorCount,
        memoryUsage: Math.round(this.memoryStats.memoryUsage / 1024 / 1024) + 'MB',
        totalCleanups: this.memoryStats.totalCleanups
      });
    }
  }

  /**
   * Get system status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      uptime: Date.now() - this.startTime,
      memoryStats: this.memoryStats,
      systemHealth: this.systemHealth,
      activeTimeouts: this.activeTimeouts.size,
      activeIntervals: this.activeIntervals.size,
      eventListeners: this.eventListeners.size
    };
  }

  /**
   * Destroy memory manager
   */
  destroy() {
    console.log('🧠 Memory Manager: Shutting down...');
    
    // Clear all cleanup intervals
    this.cleanupIntervals.forEach(interval => clearInterval(interval));
    this.cleanupIntervals.clear();
    
    // Perform final cleanup
    this.performEmergencyCleanup();
    
    this.isInitialized = false;
    console.log('🧠 Memory Manager: Shutdown completed');
  }
}

// Create singleton instance
export const memoryManager = new MemoryManager();

// Export default
export default memoryManager;
